<?php

class Logger {
	private $scriptName;

	private $allowed_flags = [
		'VERBOSE',
		'EMERGENCY',
		'ALERT',
		'CRITICAL',
		'ERROR',
		'WARNING',
		'NOTICE',
		'INFO',
		'DEBUG'
	];

	function __construct($scriptName) {
		$this->scriptName = $scriptName;
	}

	function write($data)
	{
		$flag = isset($data['flag']) && in_array($data['flag'], $this->allowed_flags) ? $data['flag'] : null;

		$message = isset($data['message']) ? $data['message'] : null;

		$login = isset($data['login']) ? $data['login'] : null;

		$payload = isset($data['payload']) ? (is_array($data['payload']) ? json_encode($data['payload']) : $data['payload']) : json_encode([]);

		$payload = str_replace('\n', '', $payload);
		$payload = str_replace('\t', '', $payload);

		$now = date('d/m/Y - H:i:s');

		echo "[$now] - [$flag] $this->scriptName: $message";

		if ($login != '')
			echo ", login: $login";

		if ($payload != '[]')
			echo ", payload: $payload";

		echo PHP_EOL;

		$dbh_noc = getConnectionNoc();
		$stmt = $dbh_noc->prepare("INSERT INTO public.ixc_scripts_logs (script, flag, message, login, payload) VALUES (?, ?, ?, ?, ?);");
		$stmt->execute([
			$this->scriptName,
			$flag,
			$message,
			$login,
			$payload
		]);
	}
}