<?php
date_default_timezone_set('America/Sao_Paulo');
require __DIR__ . '/WebserviceClient.php';
require __DIR__ . '/utils.php';

if (!function_exists('curl_reset')) {
	function curl_reset(&$ch) {
		$ch = curl_init();
	}
}

// Número máximo de IPs disponíveis por pool, para alertar sobre quantidade baixa de IPs disponíveis
define('IPS_QTY_WARNING', 10);

define('SERVIDOR_PG', 'pgsql-noc.pocos-net.com.br');
define('USUARIO_PG', 'noc2');
define('SENHA_PG', 'noc2@2015!');
define('BANCO_PG', 'noc2');

define('RADIUS_HOST', '*************');
define('RADIUS_DB', 'dbradius');
define('RADIUS_USER', 'dbradius');
define('RADIUS_PASS', 'dbradius@2016!');

define('INTERFOCUS_HOST', '*************');
define('INTERFOCUS_DB', 'interfocus');
define('INTERFOCUS_USER', 'ixc');
define('INTERFOCUS_PASS', '*.Telemidia@2020');

define('IXC_WEBSERVICE_URL', 'https://central.telemidia.net.br/webservice/v1');
define('IXC_WEBSERVICE_TOKEN', '2:53634997c36c23d451158d695685b2a821b20e8748c868fa6c7a29f0fcf4d632');

define('IXC_DB_IP', '*************');
define('IXC_DB_USER', 'leitura');
define('IXC_DB_PASS', 'RuskINHanaKeTATRaNaT');
define('IXC_DB_NAME', 'ixcprovedor');

define('ICEWARP_DB_SERVER', 'mysql-services1.pocos-net.com.br');
define('ICEWARP_DB_USER', 'root');
define('ICEWARP_DB_PW', 'pNETmys2017@Adm');
define('ICEWARP_DB_NAME', 'icewarp_data');

define('RECOVERY_DB_SERVER', 'mysql-services1.pocos-net.com.br');
define('RECOVERY_DB_USER', 'root');
define('RECOVERY_DB_PW', 'pNETmys2017@Adm');
define('RECOVERY_DB_NAME', 'ixc_recovery');

define('SERVIDOR_MARKETING', 'pgsql-services1.pocos-net.com.br');
define('USUARIO_MARKETING', 'marketing_scripts');
define('SENHA_MARKETING', 'kd981JIrSAw983$Qh518y');
define('BANCO_MARKETING', 'marketing');

function getConnectionIxc()
{
	$charset = 'utf8';
	$collate = 'utf8_unicode_ci';
	$host = IXC_DB_IP;
	$dbname = IXC_DB_NAME;
	$username = IXC_DB_USER;
	$password = IXC_DB_PASS;
	$dsn = "mysql:host=$host;dbname=$dbname;charset=$charset";

	$options = [
		PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
		PDO::ATTR_PERSISTENT => false,
		PDO::ATTR_EMULATE_PREPARES => false,
		PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
		PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES $charset COLLATE $collate"
	];

	$pdo = new PDO($dsn, $username, $password, $options);
	return $pdo;
}

function getIxcApi()
{
	$host = IXC_WEBSERVICE_URL;
	$token = IXC_WEBSERVICE_TOKEN;
	$selfSigned = true;
	$api = new IXCsoft\WebserviceClient($host, $token, $selfSigned);

	return $api;
}

function getConnectionNoc()
{

	$dbh = new PDO('pgsql:dbname=' . BANCO_PG . ';host=' . SERVIDOR_PG . ';user=' . USUARIO_PG . ';password=' . SENHA_PG);
	$dbh->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
	$dbh->setAttribute(PDO::ATTR_ORACLE_NULLS, PDO::NULL_TO_STRING);
	return $dbh;
};

function getConnectionInterfocus()
{
	$dbh_interfocus = new PDO('pgsql:dbname=' . INTERFOCUS_DB . ';host=' . INTERFOCUS_HOST . ';user=' . INTERFOCUS_USER . ';password=' . INTERFOCUS_PASS);
	$dbh_interfocus->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
	$dbh_interfocus->setAttribute(PDO::ATTR_ORACLE_NULLS, PDO::NULL_TO_STRING);

	return $dbh_interfocus;
}

function getConnectionRadius()
{
	$dbh_radius = new PDO('pgsql:dbname=' . RADIUS_DB . ';host=' . RADIUS_HOST . ';user=' . RADIUS_USER . ';password=' . RADIUS_PASS);
	$dbh_radius->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
	$dbh_radius->setAttribute(PDO::ATTR_ORACLE_NULLS, PDO::NULL_TO_STRING);

	return $dbh_radius;
}

function getConnectionIcewarp() {
	$charset = 'utf8';
	$collate = 'utf8_unicode_ci';
	$host = ICEWARP_DB_SERVER;
	$dbname = ICEWARP_DB_NAME;
	$username = ICEWARP_DB_USER;
	$password = ICEWARP_DB_PW;
	$dsn = "mysql:host=$host;dbname=$dbname;charset=$charset";
  
	$options = [
	  PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
	  PDO::ATTR_PERSISTENT => false,
	  PDO::ATTR_EMULATE_PREPARES => true,
	  PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
	  PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES $charset COLLATE $collate"
	];
  
	$pdo = new PDO($dsn, $username, $password, $options);
	return $pdo;
  }

function getConnectionRecovery() {
	$charset = 'utf8';
	$collate = 'utf8_unicode_ci';
	$host = RECOVERY_DB_SERVER;
	$dbname = RECOVERY_DB_NAME;
	$username = RECOVERY_DB_USER;
	$password = RECOVERY_DB_PW;
	$dsn = "mysql:host=$host;dbname=$dbname;charset=$charset";
  
	$options = [
	  PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
	  PDO::ATTR_PERSISTENT => false,
	  PDO::ATTR_EMULATE_PREPARES => true,
	  PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
	  PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES $charset COLLATE $collate"
	];
  
	$pdo = new PDO($dsn, $username, $password, $options);
	return $pdo;
  }

function getConnectionMarketing()
{
	$dbh = new PDO('pgsql:dbname=' . BANCO_MARKETING . ';host=' . SERVIDOR_MARKETING . ';user=' . USUARIO_MARKETING . ';password=' . SENHA_MARKETING);
	$dbh->setAttribute(PDO::ATTR_PERSISTENT, true);
	$dbh->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
	$dbh->setAttribute(PDO::ATTR_ORACLE_NULLS, PDO::NULL_TO_STRING);
	return $dbh;
}