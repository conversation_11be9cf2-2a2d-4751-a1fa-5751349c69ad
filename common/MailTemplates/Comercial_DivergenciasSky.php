<?php
$COMPANY_INFO = json_decode($COMPANY_INFO, true);
$DIVERGENCIAS_SKY_INFO = json_decode($DIVERGENCIAS_SKY_INFO, true);
?>

<head>
    <style>
        #outlook a {
            padding: 0;
        }

        body {
            margin: 0;
            padding: 0;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }

        table,
        td {
            border-collapse: collapse;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }

        img {
            border: 0;
            height: auto;
            line-height: 100%;
            outline: none;
            text-decoration: none;
            -ms-interpolation-mode: bicubic;
        }

        p {
            display: block;
            margin: 13px 0;
        }

        .table-dados {
            margin: 0 auto;
        }

        .table-dados th,
        .table-dados td.spacer {
            background: #EEE;
        }

        .table-dados td.spacer {
            padding: 0px;
        }

        .table-dados td,
        .table-dados th {
            border: 1px solid #CCC;
            padding: 3px 7px;
            text-align: center;
            font-weight: normal;
        }

        .btn-help-container {
            width: 100%;
            text-align: center;
        }

        .btn-help {
            padding: 4px 8px;
            background: #EEE;
            border: 1px solid #2FA4E7;
            border-radius: 5px;
            text-decoration: none;
        }
    </style>
</head>

<body style="background: #F7F7F7;">
    <!-- START LIQUID WRAPPER -->
    <!--[if mso]>
<table cellpadding="0" cellspacing="0" border="0" style="padding:0px;margin:0px;width:100%;">
	
<![endif]-->
    <tr>
        <td colspan="3" style="padding:0px;margin:0px;font-size:20px;height:20px;" height="20">&nbsp;</td>
    </tr>

    <!--[if mso]>
	<tr>
		<td style="padding:0px;margin:0px;">&nbsp;</td>
		<td style="padding:0px;margin:0px;" width="800" bgcolor="#FFFFFF">
			<![endif]-->

    <div style="background: #FFF; max-width: 800px; margin: 0 auto; border: 1px solid #D9D9D9;">
        <table width="800">
            <tr>
                <td align="center" width="800" height="65" bgcolor="#00416B" style="background: #00416b; border: 1px solid #D9D9D9;">
                    <table width="800">
                        <tr>
                            <td style="padding-left: 15px;">
                                <img src="cid:telemidia_light" width="90" />
                            </td>
                            <td style="color: #FFF; font-weight: bold; font-size: 15pt; padding-left: 80px;">
                                Verificações IXC
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>

        <!-- START LIQUID WRAPPER -->
        <!--[if mso]>
			<table cellpadding="0" cellspacing="0" border="0" style="padding:0px;margin:0px;width:100%;">
				<tr>
					<td colspan="3" style="padding:0px;margin:0px;font-size:20px;height:20px;" height="20">&nbsp;</td>
				</tr>
				<tr>
					<td style="padding:0px;margin:0px;">&nbsp;</td>
					<td style="padding:0px;margin:0px;" width="710" bgcolor="#FFFFFF">
						<![endif]-->

        <div style="background: #FFF; margin: 0 auto; padding: 30px;">
            <div style="text-align: justify;">
                Olá, setor Comercial!
                <br>
                <br>
                Foram encontradas divergências no IXC, em relação aos produtos e/ou configuração da SKY+ nos contratos:
                <br>
                <br>
            </div>


            <?php
            foreach ($divergenciasSky as $grupo => $divergencias) {
                $info = $DIVERGENCIAS_SKY_INFO[$grupo];
                if (sizeof($divergencias) < 1)
                    continue;
            ?>
                <table width="710">
                    <tr>
                        <td align="center" width="710" height="20">
                            &nbsp;
                        </td>
                    </tr>
                </table>
                <table width="710">
                    <tr>
                        <td align="center" width="710" height="1" style="border-top: 1px solid #DDD;">
                            &nbsp;
                        </td>
                    </tr>
                </table>
                <div style="text-align: justify;">
                    <div style="text-align: center;">
                        <b><?= $info['titulo'] ?></b>:
                    </div>
                    <div style="margin-top: 10px;">
                        <table class="table-dados">
                        <?php
                        if(in_array($grupo, [
                            'contratosComProdutosTipoIncorreto',
                            'contratosDgoSemUsuarioTv',
                            'usuariosTvSemPacoteDgo'
                        ])) {
                            ?>
                                <thead>
                                    <tr>
                                        <th>ID do contrato</th>
                                    </tr>
                                </thead>
                                <tbody>
                                <?php
                                foreach($divergencias as $divergencia) {
                                    ?>
                                    <tr>
                                        <td><?= $divergencia ?></td>
                                    </tr>
                                    <?php
                                }
                                ?>
                                </tbody>
                            <?php
                        }

                        else if(in_array($grupo, [
                            'usuariosTvSemIntegracaoPlayhub',
                            'usuariosIxcSemUsuarioAbranet'
                        ])) {
                            ?>
                                <thead>
                                    <tr>
                                        <th>ID do contrato</th>
                                        <th>Usuário de TV</th>
                                    </tr>
                                </thead>
                                <tbody>
                                <?php
                                foreach($divergencias as $divergencia) {
                                    ?>
                                    <tr>
                                        <td><?= $divergencia['id_contrato'] ?></td>
                                        <td><?= $divergencia['usuario'] ?></td>
                                    </tr>
                                    <?php
                                }
                                ?>
                                </tbody>
                            <?php
                        }

                        else if($grupo === 'usuariosAbranetSemUsuarioIxc') {
                            ?>
                                <thead>
                                    <tr>
                                        <th>E-mail</th>
                                        <th>Nome</th>
                                        <th>Documento</th>
                                    </tr>
                                </thead>
                                <tbody>
                                <?php
                                foreach($divergencias as $divergencia) {
                                    ?>
                                    <tr>
                                        <td><?= $divergencia['email'] ?></td>
                                        <td><?= $divergencia['name'] ?></td>
                                        <td><?= $divergencia['document'] ?></td>
                                    </tr>
                                    <?php
                                }
                                ?>
                                </tbody>
                            <?php
                        }

                        else if($grupo === 'contratosComPacotesDivergentes') {
                            ?>
                                <thead>
                                    <tr>
                                        <th>ID contrato</th>
                                        <th>Pacotes IXC</th>
                                        <th>Pacotes Abranet</th>
                                    </tr>
                                </thead>
                                <tbody>
                                <?php
                                foreach($divergencias as $divergencia) {
                                    ?>
                                    <tr>
                                        <td><?= $divergencia['id_contrato'] ?></td>
                                        <td><?= $divergencia['pacotes_ixc'] ?></td>
                                        <td><?= $divergencia['pacotes_abranet'] ?></td>
                                    </tr>
                                    <?php
                                }
                                ?>
                                </tbody>
                            <?php
                        }
                        ?>
                        </table>
                    </div>

                    <table width="710">
                        <tr>
                            <td align="center" width="710" height="20">
                                &nbsp;
                            </td>
                        </tr>
                    </table>
                    <div class="btn-help-container">
                        <a class="btn-help" href="<?= $info['helpUrl'] ?>">Como corrigir?</a>
                    </div>
                </div>
            <?php
            }
            ?>
            <table width="710">
                <tr>
                    <td align="center" width="710" height="20">
                        &nbsp;
                    </td>
                </tr>
            </table>
            <table width="710">
                <tr>
                    <td align="center" width="710" height="1" style="border-top: 1px solid #DDD;">
                        &nbsp;
                    </td>
                </tr>
            </table>
            <div style="background: #FFF; margin: 0 auto; padding: 30px;">
                <div style="text-align: justify;">
                    Após realizar as correções, aguarde 5 minutos e verifique novamente a <a href="https://noc.telemidia.net.br/comercial/sky">lista de divergências da SKY+, no NOC</a>.
                </div>
            </div>
            <div style="background: #FFF; margin: 0 auto; padding: 30px; padding-top: 0px;">
                <div style="text-align: center;">
                    Caso tenha dúvidas,
                    <br>
                    <a href="mailto:<EMAIL>">contacte o COR</a>
                </div>
            </div>
        </div>
        <!--[if mso]>
					</td>
					<td style="padding:0px;margin:0px;">&nbsp;</td>
				</tr>
				<tr>
					<td colspan="3" style="padding:0px;margin:0px;font-size:20px;height:20px;" height="20">&nbsp;</td>
				</tr>
			</table>
			<![endif]-->
        <!-- END LIQUID WRAPPER -->
    </div>
    <!--[if mso]>
			</td>
			<td style="padding:0px;margin:0px;">&nbsp;</td>
			</tr>
			</table>
			<![endif]-->
    <!-- END LIQUID WRAPPER -->
    <!-- START LIQUID WRAPPER -->
    <!--[if mso]>
			<table cellpadding="0" cellspacing="0" border="0" style="padding:0px;margin:0px;width:100%;">
				<tr>
					<td style="padding:0px;margin:0px;">&nbsp;</td>
					<td style="padding:0px;margin:0px;" align="center" width="800" bgcolor="#F7F7F7">
						<![endif]-->

    <div style="background: #FAFAFA; text-align: center;">
        <table>
            <tr>
                <td height="30">&nbsp;</td>
            </tr>
        </table>

        <table style="margin: 0 auto !important; color: #777777;">
            <tr>
                <td align="center">
                    <img src="cid:telemidia_dark" width="90" style="margin-bottom: 20px;" />
                </td>
            </tr>
            <!--[if mso]>
				<table>
					<tr>
						<td height="20">&nbsp;</td>
					</tr>
				</table>
				<![endif]-->
            <tr>
                <td align="center"><?= $COMPANY_INFO['matriz']['name'] ?></td>
            </tr>
            <tr>
                <td height="10"></td>
            </tr>
            <tr>
                <td align="center">
                    ************
                    <br>
                    /var/www/ixc_scripts/cronjobs/checar_divergencias_sky
                </td>
            </tr>
        </table>

        <table>
            <tr>
                <td height="50">&nbsp;</td>
            </tr>
        </table>
    </div>
    <!--[if mso]>
			</td>
			<td style="padding:0px;margin:0px;">&nbsp;</td>
		</tr>
		<tr>
			<td colspan="3" style="padding:0px;margin:0px;font-size:20px;height:20px;" height="20">&nbsp;</td>
		</tr>
	</table>
	<![endif]-->
    <!-- END LIQUID WRAPPER -->
</body>