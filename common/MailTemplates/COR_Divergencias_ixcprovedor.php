<?php
$COMPANY_INFO = json_decode($COMPANY_INFO, true);
?>

<head>
    <style>
        #outlook a {
            padding: 0;
        }

        body {
            margin: 0;
            padding: 0;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }

        table,
        td {
            border-collapse: collapse;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }

        img {
            border: 0;
            height: auto;
            line-height: 100%;
            outline: none;
            text-decoration: none;
            -ms-interpolation-mode: bicubic;
        }

        p {
            display: block;
            margin: 13px 0;
        }

        .table-dados {
            margin: 0 auto;
        }

        .table-dados th,
        .table-dados td.spacer {
            background: #EEE;
        }

        .table-dados td.spacer {
            padding: 0px;
        }

        .table-dados td,
        .table-dados th {
            border: 1px solid #CCC;
            padding: 3px 7px;
            text-align: center;
            font-weight: normal;
        }

        .btn-help-container {
            width: 100%;
            text-align: center;
        }

        .btn-help {
            padding: 4px 8px;
            background: #EEE;
            border: 1px solid #2FA4E7;
            border-radius: 5px;
            text-decoration: none;
        }

        .removed {
            color: #FF0000; /* Vermelho para itens removidos */
            font-weight: bold;
        }

        .added {
            color: #008000; /* Verde para itens adicionados */
            font-weight: bold;
        }

        .modified {
            color: #FF8C00; /* Laranja para tabelas modificadas */
            font-weight: bold;
        }

        hr.divider {
            border: 0;
            height: 1px;
            background-color: #CCC;
            margin: 20px 0;
        }
    </style>
</head>

<body style="background: #F7F7F7;">
    <!-- START LIQUID WRAPPER -->
    <!--[if mso]>
<table cellpadding="0" cellspacing="0" border="0" style="padding:0px;margin:0px;width:100%;">

<![endif]-->
    <tr>
        <td colspan="3" style="padding:0px;margin:0px;font-size:20px;height:20px;" height="20">&nbsp;</td>
    </tr>

    <!--[if mso]>
	<tr>
		<td style="padding:0px;margin:0px;">&nbsp;</td>
		<td style="padding:0px;margin:0px;" width="800" bgcolor="#FFFFFF">
			<![endif]-->

    <div style="background: #FFF; max-width: 800px; margin: 0 auto; border: 1px solid #D9D9D9;">
        <table width="800">
            <tr>
                <td align="center" width="800" height="65" bgcolor="#00416B" style="background: #00416b; border: 1px solid #D9D9D9;">
                    <table width="800">
                        <tr>
                            <td style="padding-left: 15px;">
                                <img src="cid:telemidia_light" width="90" />
                            </td>
                            <td style="color: #FFF; font-weight: bold; font-size: 15pt; padding-left: 80px;">
                                Monitoramento DB ixcprovedor
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>

        <!-- START LIQUID WRAPPER -->
        <!--[if mso]>
			<table cellpadding="0" cellspacing="0" border="0" style="padding:0px;margin:0px;width:100%;">
				<tr>
					<td colspan="3" style="padding:0px;margin:0px;font-size:20px;height:20px;" height="20">&nbsp;</td>
				</tr>
				<tr>
					<td style="padding:0px;margin:0px;">&nbsp;</td>
					<td style="padding:0px;margin:0px;" width="710" bgcolor="#FFFFFF">
						<![endif]-->

        <div style="background: #FFF; margin: 0 auto; padding: 30px;">
            <div style="text-align: justify;">
                Olá, COR!
                <br>
                <br>
                Foram encontradas alterações na estrutura de banco de dados do IXC desde a última checagem em <b><?= date('d/m/Y H:i:s', strtotime($lastSchemaDate)) ?></b>:
                <br>
                <br>
            </div>

            <div>
                <?php
                $isFirst = true;
                foreach ($schemaChanges as $change) {
                    // Adicionar linha horizontal divisória entre as tabelas (exceto antes da primeira)
                    if (!$isFirst) {
                        echo '<hr class="divider">';
                    } else {
                        $isFirst = false;
                    }

                    // Dividir a mensagem em linhas para processar cada uma separadamente
                    $lines = explode(PHP_EOL, $change);
                    $processedLines = [];

                    // Processar cada linha individualmente
                    foreach ($lines as $index => $line) {
                        // Verificar se a linha contém informações sobre tabela modificada
                        if (strpos($line, 'modificada') !== false) {
                            $processedLines[] = '<span class="modified">' . $line . '</span>';
                        }
                        // Verificar se a linha contém informações sobre remoção
                        elseif (strpos($line, 'removida') !== false || strpos($line, 'Colunas removidas') !== false) {
                            $processedLines[] = '<span class="removed">' . $line . '</span>';
                        }
                        // Verificar se a linha contém informações sobre adição
                        elseif (strpos($line, 'adicionada') !== false || strpos($line, 'Colunas adicionadas') !== false) {
                            $processedLines[] = '<span class="added">' . $line . '</span>';
                        }
                        // Caso contrário, manter a linha como está
                        else {
                            $processedLines[] = $line;
                        }
                    }

                    // Juntar as linhas processadas
                    $processedChange = nl2br(implode(PHP_EOL, $processedLines));
                ?>
                    <div>
                        <p><?= $processedChange ?></p>
                    </div>
                <?php
                }
                ?>
            </div>
        </div>
        <!--[if mso]>
					</td>
					<td style="padding:0px;margin:0px;">&nbsp;</td>
				</tr>
				<tr>
					<td colspan="3" style="padding:0px;margin:0px;font-size:20px;height:20px;" height="20">&nbsp;</td>
				</tr>
			</table>
			<![endif]-->
        <!-- END LIQUID WRAPPER -->
    </div>
    <!--[if mso]>
			</td>
			<td style="padding:0px;margin:0px;">&nbsp;</td>
			</tr>
			</table>
			<![endif]-->
    <!-- END LIQUID WRAPPER -->
    <!-- START LIQUID WRAPPER -->
    <!--[if mso]>
			<table cellpadding="0" cellspacing="0" border="0" style="padding:0px;margin:0px;width:100%;">
				<tr>
					<td style="padding:0px;margin:0px;">&nbsp;</td>
					<td style="padding:0px;margin:0px;" align="center" width="800" bgcolor="#F7F7F7">
						<![endif]-->

    <div style="background: #FAFAFA; text-align: center;">
        <table>
            <tr>
                <td height="30">&nbsp;</td>
            </tr>
        </table>

        <table style="margin: 0 auto !important; color: #777777;">
            <tr>
                <td align="center">
                    <img src="cid:telemidia_dark" width="90" style="margin-bottom: 20px;" />
                </td>
            </tr>
            <!--[if mso]>
				<table>
					<tr>
						<td height="20">&nbsp;</td>
					</tr>
				</table>
				<![endif]-->
            <tr>
                <td align="center"><?= $COMPANY_INFO['matriz']['name'] ?></td>
            </tr>
            <tr>
                <td height="10"></td>
            </tr>
            <tr>
                <td align="center">
                    ************
                    <br>
                    /var/www/ixc_scripts/cronjobs/checar_divergencias
                </td>
            </tr>
        </table>

        <table>
            <tr>
                <td height="50">&nbsp;</td>
            </tr>
        </table>
    </div>
    <!--[if mso]>
			</td>
			<td style="padding:0px;margin:0px;">&nbsp;</td>
		</tr>
		<tr>
			<td colspan="3" style="padding:0px;margin:0px;font-size:20px;height:20px;" height="20">&nbsp;</td>
		</tr>
	</table>
	<![endif]-->
    <!-- END LIQUID WRAPPER -->
</body>