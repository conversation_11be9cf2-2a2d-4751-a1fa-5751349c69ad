<?php
$login_a_executar = 'testefibra100';

require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/Logger.php';

$f = fopen('lock', 'w') or die('Cannot create lock file');
if (flock($f, LOCK_EX | LOCK_NB)) {
	main();
}

function main() {
	$this_execution_time = date('Y-m-d H:i:s');
	// Busca o horário de última execução, do arquivo "last_execution_time"
	$last_execution_time = file_get_contents(__DIR__ . '/last_execution_time');

	// Checa se o valor do arquivo "last_execution_time" é um DateTime válido. Senão, seta o valor da última execução para 24 horas atrás
	if (!isDatetime($last_execution_time)) {
		$last_execution_time = date('Y-m-d H:i:s', strtotime('-24 hours', strtotime($this_execution_time)));
	}

	define('THIS_EXECUTION_TIME', $this_execution_time);
	define('LAST_EXECUTION_TIME', $last_execution_time);

	writeLog('THIS_EXECUTION_TIME: ' . THIS_EXECUTION_TIME);
	writeLog('LAST_EXECUTION_TIME: ' . LAST_EXECUTION_TIME);

	//writeLog('Iniciando função syncDeletedLogins()...');
	//syncDeletedLogins();

	writeLog('Iniciando função syncInsertedOrUpdatedLogins()...');
	syncInsertedOrUpdatedLogins();

	writeLog('FINALIZANDO cronjob ixc_logins_syncer...');
	writeLog('------------------------------------------------');

	// Atualiza o horário de última execução, após finalizar as funções anteriores
	file_put_contents(__DIR__ . '/last_execution_time', $this_execution_time);

	// Indica que finalizou a execução
	file_put_contents(__DIR__ . '/is_executing', '0');
}

// ------------------------------------------------------------

function syncDeletedLogins()
{

	$dbh_ixc = getConnectionIXC();
	$dbh_interfocus = getConnectionInterfocus();

	writeLog('Buscando logs de exclusão de usernames do IXC...');

	// Lista LOGs de exclusão de logins (no log consta apenas o ID do login excluido, nao o login)
	$stmt = $dbh_ixc->prepare("SELECT * FROM ixc_logs
	WHERE tabela = 'radusuarios'
	AND tipo = 'excluiu'
		AND data BETWEEN ? AND ?
	ORDER BY data ASC;");

	$stmt->execute(array(LAST_EXECUTION_TIME, THIS_EXECUTION_TIME));
	$logs = $stmt->fetchAll(PDO::FETCH_ASSOC);

	// Para cada log de exclusão, insere um registro na cad_user_buffer
	foreach ($logs as $log) {
		writeLog("Adicionando registro de exclusão (codigoocorrencia=DX) na cad_user_buffer com idequipamento = $log[id_tabela] ...");

		$query = "INSERT INTO public.cad_user_buffer (
			codigoocorrencia,
			idequipamento,
			username,
			senha,
			situacaoequipamento,
			situacaocontrato,
			idplano,
			dia,
			mac,
			ap,
			ipap,
			idcontrato,
			escopo,
			ipfixo
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);";

		$values = array(
			'DX',
			$log['id_tabela'],
			'',
			'',
			0,
			0,
			0,
			1,
			'',
			0,
			'0.0.0.0',
			0,
			0,
			NULL
		);

		var_dump($values);

		try {
			$stmt = $dbh_interfocus->prepare($query);
			$stmt->execute($values);
		} catch (PDOException $e) {
			echo "Database error: " . $e->getMessage();
		} catch (Exception $e) {
			echo "General error: " . $e->getMessage();
		}
	}
}

function syncInsertedOrUpdatedLogins()
{

	global $login_a_executar;

	$dbh_ixc = getConnectionIXC();
	$dbh_radius = getConnectionRadius();
	$dbh_interfocus = getConnectionInterfocus();


	writeLog("Listando usuários que tiveram modificação desde LAST_EXECUTION_TIME...");

	// Lista os usuários que tiveram atualização (INSERT/UPDATE) desde a última execução DESTE SCRIPT
	// 	$stmt = $dbh_ixc->prepare("SELECT l.tipo AS log_tipo, l.id_tabela, l.data AS log_data, rpr.descricao AS transmissor, r.* FROM ixc_logs l
	// INNER JOIN radusuarios r ON r.id = l.id_tabela
	// LEFT JOIN radpop_radio rpr ON rpr.id = r.id_transmissor
	// LEFT JOIN radpop rp ON rp.id = rpr.id_pop
	// WHERE l.tabela = 'radusuarios' AND l.data BETWEEN ? AND ? AND l.operador != 2 AND l.tipo != 'excluiu'
	// ORDER BY l.data ASC;");

	$stmt = $dbh_ixc->prepare("SELECT l.tipo AS log_tipo, l.id_tabela, l.data AS log_data, rpr.descricao AS transmissor, r.* FROM ixc_logs l
INNER JOIN radusuarios r ON r.id = l.id_tabela
LEFT JOIN radpop_radio rpr ON rpr.id = r.id_transmissor
LEFT JOIN radpop rp ON rp.id = rpr.id_pop
WHERE r.login = ?
ORDER BY l.data ASC
LIMIT 1;");

	try {
		$stmt->execute(array($login_a_executar));
		$radusuarios = $stmt->fetchAll(PDO::FETCH_ASSOC);
	} catch (PDOException $e) {
		echo "Database error: " . $e->getMessage();
	} catch (Exception $e) {
		echo "General error: " . $e->getMessage();
	}

	// Para cada login que teve alteração:
	// 1 - Coleta os dados do login (senha, cidade etc)
	// 2 - Insere na cad_user_buffer, com o codigo de ocorrencia 'IX' (referente a Inserção do iXc)
	// 3 - Insere um vínculo de "login <-> ID", na tabela dbradius.public.ixc_logins, para futura relação com o ID, no ato da exclusão do login
	// 4 - Busca o Framed IPv6 e o Delegated IPv6 gerados no radius e preenche nos respectivos campos do Login, no IXC
	foreach ($radusuarios as $radusuario) {

		// Para executar somente em 1 login
		if ($radusuario['login'] !== $login_a_executar)
			continue;

		writeLog("Buscando informações do usuário $radusuario[login]...");

		// 1 - Coleta os dados do login (senha, cidade etc)
		$id_plano_ixc = $radusuario['id_grupo'];

		$id_cad_planos = getCadPlanosId($id_plano_ixc);

		if (!$id_cad_planos) {
			writeLog("Pulando usuário '$radusuario[login]'... Plano não encontrado na base de dados do dbradius: ID_PLANO_IXC: $id_plano_ixc");
			continue;
		}

		$transmissor = $radusuario['transmissor'];
		$cidade = ixc_getCidadeByRadusuario($radusuario);

		if ($transmissor && trim($transmissor) != '') {
			$ap = $transmissor;
			$escopo = $transmissor;
		} else if ($cidade != 0) {
			$ap = $cidade;
			$escopo = $cidade;
		} else {
			writeLog("Pulando usuário '$radusuario[login]'... Não foi possível definir a cidade do usuário");
			continue;
		}

		$ativo = $radusuario['ativo'] == 'S' ? 1 : 0;


		writeLog("Inserindo na cad_user_buffer, usuário $radusuario[login]...");

		// 2 - Insere na cad_user_buffer, com o codigo de ocorrencia 'IX' (referente a Inserção do iXc)
		$query = "INSERT INTO public.cad_user_buffer (
			codigoocorrencia,
			idequipamento,
			username,
			senha,
			situacaoequipamento,
			situacaocontrato,
			idplano,
			dia,
			mac,
			ap,
			ipap,
			idcontrato,
			escopo,
			ipfixo
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);";

		$values = array(
			'IX',
			$radusuario['id'],
			$radusuario['login'],
			$radusuario['senha'],
			$ativo,
			$ativo,
			$id_cad_planos,
			1,
			$radusuario['mac'],
			$ap,
			'0.0.0.0',
			$radusuario['id_contrato'],
			$escopo,
			$radusuario['ip']
		);

		writeLog('VALORES da CAD_USER_BUFFER:');
		writeLog(var_export($values, true));

		var_dump($values);
		// return;

		var_dump($query);
		var_dump($values);

		try {
			$stmt = $dbh_interfocus->prepare($query);
			$query = $stmt->execute($values);
		} catch (PDOException $e) {
			echo "Database error: " . $e->getMessage();
			writeLog("Database error: " . $e->getMessage());
		} catch (Exception $e) {
			echo "General error: " . $e->getMessage();
			writeLog("General error: " . $e->getMessage());
		}

		//writeLog(var_export($values, true));

		if (!$query) {
			writeLog("Houve um erro ao inserir o usuário $radusuario[login] na tabela cad_users_buffer. Pulando usuário...");
			continue;
		}

		writeLog('O usuário foi inserido com sucesso.');

		try {
			// 3 - Insere um vínculo de "login <-> ID", na tabela dbradius.public.ixc_logins, para futura relação com o ID, no ato da exclusão do login
			$stmt = $dbh_radius->prepare("INSERT INTO public.ixc_logins (id_ixc, login) VALUES (?, ?) ON CONFLICT (id_ixc) DO UPDATE SET login = ?;");
			$stmt->execute(array($radusuario['id'], $radusuario['login'], $radusuario['login']));
		} catch (PDOException $e) {
			echo "Database error: " . $e->getMessage();
		} catch (Exception $e) {
			echo "General error: " . $e->getMessage();
		}


		writeLog('Buscando IPv6 do usuário (tabela radipv6_allocated)...');
		try {
			// 4 - Busca o Framed IPv6 e o Delegated IPv6 gerados no radius e preenche nos respectivos campos do Login, no IXC
			$stmt = $dbh_radius->prepare("SELECT * FROM public.radipv6_allocated WHERE username = ?;");
			$stmt->execute(array($radusuario['login']));
			$radipv6_rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
		} catch (PDOException $e) {
			echo "Database error: " . $e->getMessage();
		} catch (Exception $e) {
			echo "General error: " . $e->getMessage();
		}

		$framed_ipv6 = $delegated_ipv6 = null;
		foreach ($radipv6_rows as $row) {
			if ($row['attribute'] == 'Framed-IPv6-Prefix')
				$framed_ipv6 = $row['allocated_prefix'];
			else if ($row['attribute'] == 'Delegated-Ipv6-Prefix')
				$delegated_ipv6 = $row['allocated_prefix'];
		}

		if ($framed_ipv6 && $delegated_ipv6) {
			writeLog('Inserindo IPv6 do usuário no IXC...');

			$radusuario['pd_ipv6'] = $delegated_ipv6;
			$radusuario['framed_pd_ipv6'] = $framed_ipv6;

			$radusuario['fixar_ipv6'] = 'S';
			$radusuario['framed_fixar_ipv6'] = 'S';

			$retorno = ixc_updateRadusuario($radusuario);
			var_dump($retorno);
		} else {
			writeLog('[ERRO]: não foi possível definir a faixa de IPv6 do cliente.');
		}
	}
}


// ----------------------------------------------------------------------------


function ixc_updateRadusuario($radusuario)
{
	$api = getIxcApi();

	unset(
		$radusuario['log_tipo'],
		$radusuario['id_tabela'],
		$radusuario['log_data'],
		$radusuario['transmissor']
	);

	foreach ($radusuario as &$value)
		if (gettype($value) === 'string')
			$value = utf8_encode($value);

	if ($radusuario['ip'] && trim($radusuario['ip']) != '')
		$radusuario['fixar_ip'] = 'S';

	$registro = $radusuario['id']; //registro a ser editado
	$api->put('radusuarios', $radusuario, $registro);
	$resposta = $api->getRespostaConteudo(true); // false para json | true para array

	return $resposta;

	// $api = getIxcApi();

	// // Remove os índices do objeto que vieram de INNER/LEFT JOINS (campos que não constam na tabela `radusuarios`)
	// unset(
	// 	$radusuario['log_tipo'],
	// 	$radusuario['id_tabela'],
	// 	$radusuario['log_data'],
	// 	$radusuario['transmissor']
	// );

	// if($radusuario['ip'] && trim($radusuario['ip']) != '') {
	// 	$radusuario['fixar_ip'] = 'S';
	// }

	// writeLog('Fazendo update do usuário:');
	// writeLog(var_export($radusuario, true));

	// $registro = $radusuario['id'];//registro a ser editado
	// $api->put('radusuarios', $radusuario, $registro);
	// $resposta = $api->getRespostaConteudo(false);// false para json | true para array

	// return $resposta;
}


function ixc_getCidadeByRadusuario($radusuario)
{
	$dbh_ixc = getConnectionIXC();

	if ($radusuario['endereco_padrao_cliente'] == 'S' || ($radusuario['endereco_padrao_cliente'] == 'N') && $radusuario['cidade'] == 0) {
		$stmt = $dbh_ixc->prepare("SELECT cidade FROM cliente WHERE id = ?;");
		$stmt->execute(array($radusuario['id_cliente']));
		$cliente = $stmt->fetch(PDO::FETCH_ASSOC);
		$cidade = $cliente['cidade'];
	} else {
		$cidade = $radusuario['cidade'];
	}

	return $cidade;
}


function getCadPlanosId($id_plano_ixc)
{
	// Planos que estao com velocidade zerada na tabela do IXC
	if (in_array($id_plano_ixc, [
		994, 995, 996, 997, 1006
	])) {
		writeLog('!!! PLANO CADASTRADO SEM VELOCIDADE !!! PULANDO USUÁRIO...');
		return false;
	}

	$dbh_radius = getConnectionRadius();

	$stmt = $dbh_radius->prepare("SELECT r.id AS id_cad_planos FROM 
		temp.ixc_planos i
	INNER JOIN cad_planos r ON
		plano2bits(i.download) = plano2bits(r.down)
		AND plano2bits(i.upload) = plano2bits(r.up)
	WHERE i.id = ?
	LIMIT 1;");
	$query = $stmt->execute(array($id_plano_ixc));

	$id_cad_planos = $stmt->fetchColumn();

	return $id_cad_planos;
}

function isDatetime($str)
{
	return (DateTime::createFromFormat('Y-m-d H:i:s', $str) !== false);
}

function writeLog($str)
{
	$now = date('d/m/Y H:i:s');
	$str = '[' . $now . ']: ' . $str . PHP_EOL;
	file_put_contents(__DIR__ . '/logger.log', $str, FILE_APPEND);
}
