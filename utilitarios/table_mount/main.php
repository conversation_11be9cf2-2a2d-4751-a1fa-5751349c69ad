]<?php
require __DIR__ . '/../../common/config.php';

echo generateDDL('cliente_contrato');

// ---------------------------------------

function generateDDL($table) {
	$dbh_ixc = getConnectionIxc();

	$stmt = $dbh_ixc->prepare("SHOW CREATE TABLE $table");
	$stmt->execute();
	$ddl = $stmt->fetch(PDO::FETCH_ASSOC)['Create Table'];

	$post = strrpos($ddl, ')');

	$initial = substr($ddl, 0, $post);

	$pre = strpos($initial, '(');

	$main = substr($initial, $pre+1);

	$rows = explode(",\n", $main);

	foreach($rows as &$row) {
		$row = trim($row);
		$row = str_replace("`", '"', $row);
		// Retira termos NOT NULL, AUTO_INCREMENT e unsigned
		$row = str_replace("NOT NULL", "", $row);
		$row = str_replace("AUTO_INCREMENT", "", $row);
		$row = str_replace(" unsigned", "", $row);
		// Substitui date, datetime, timestamp por varchar(30),
		$row = str_replace(" date ", " varchar(30) ", $row);
		$row = str_replace(" datetime ", " varchar(30) ", $row);
		$row = str_replace(" timestamp ", " varchar(30) ", $row);
		// Substitui "*text" por "text":
		$row = str_replace(" tinytext ", " text ", $row);
		$row = str_replace(" mediumtext ", " text ", $row);
		$row = str_replace(" longtext ", " text ", $row);
		// Substitui "*blob" por "bytea":
		$row = str_replace(" tinyblob ", " bytea ", $row);
		$row = str_replace(" blob ", " bytea ", $row);
		$row = str_replace(" mediumblob ", " bytea ", $row);
		$row = str_replace(" longblob ", " bytea ", $row);

		// Substitui "DEFAULT *" por "DEFAULT NULL":
		if(str_contains($row, "DEFAULT")) {
			$exp = explode('DEFAULT', $row);
			$row = trim($exp[0]) . " DEFAULT NULL,";
		}
		// Substitui char por TEXT
		if(str_contains($row, "char(") && !str_contains($row, "varchar")) {
			$exp = explode('char(', $row);
			$exp2 = explode(')', $exp[1]);
			$row = trim($exp[0]) . " text " . trim($exp2[1]);
		}
		// Substitui smallint por int2:
		if(str_contains($row, "smallint(")) {
			$exp = explode('smallint(', $row);
			$exp2 = explode(')', $exp[1]);
			$row = trim($exp[0]) . " int2 " . trim($exp2[1]);
		}
		// Substitui bigint por int8:
		else if(str_contains($row, "bigint(")) {
			$exp = explode('bigint(', $row);
			$exp2 = explode(')', $exp[1]);
			$row = trim($exp[0]) . " int8 " . trim($exp2[1]);
		}
		// Substitui int por int4:
		else if(str_contains($row, "int(")) {
			$exp = explode('int(', $row);
			$exp2 = explode(')', $exp[1]);
			$row = trim($exp[0]) . " int4 " . trim($exp2[1]);
		}
		// Substitui enum por varchar(30):
		if(str_contains($row, "enum(")) {
			$exp = explode('enum(', $row);
			$exp2 = explode(')', $exp[1]);
			$row = trim($exp[0]) . " varchar(30) " . trim($exp2[1]);
		}

		$row = trim(trim($row, ',')) . ',';
	}

	$valid_rows = [];
	foreach($rows as $row) {
		// Ignora linhas de KEY, CONSTRAINT, PRIMARY KEY, UNIQUE KEY
		if(!startsWith($row, 'KEY')
			&& !startsWith($row, 'CONSTRAINT')
			&& !startsWith($row, 'PRIMARY KEY')
			&& !startsWith($row, 'UNIQUE KEY')
			&& !in_array($row, $valid_rows))
			$valid_rows[] = $row;
	}

	$retorno = "CREATE FOREIGN TABLE fdwtables.ixc_$table (" . PHP_EOL;

	$i = 0;
	foreach($valid_rows as $row) {
		if(sizeof($valid_rows) == ++$i)
			$row = rtrim($row, ',');

		$retorno .= "\t" . $row . PHP_EOL;

	}

	$retorno .= ")
	SERVER ixcprovedor
	OPTIONS (dbname 'ixcprovedor', table_name '$table');" . PHP_EOL;

	$retorno .= PHP_EOL . PHP_EOL . "GRANT SELECT, INSERT, UPDATE, DELETE
	ON fdwtables.ixc_$table
	TO noc2;" . PHP_EOL . PHP_EOL;

	return $retorno;
}


function str_contains($str, $check) {
	return (strpos($str, $check) !== false);
}


function startsWith ($string, $startString)
{
    $len = strlen($startString);
    return (substr($string, 0, $len) === $startString);
}


function endsWith($string, $endString)
{
    $len = strlen($endString);
    if ($len == 0) {
        return true;
    }
    return (substr(trim($string), -$len) === $endString);
}
