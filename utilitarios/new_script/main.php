<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/Logger.php';

$Logger = new Logger('new-script');

$f = fopen(__DIR__ . '/lock', 'w') or die('Cannot create lock file');
if (flock($f, LOCK_EX | LOCK_NB)) {
	main();
} else {
	$Logger->write([
		'flag' => 'INFO',
		'message' => 'script já em execução'
	]);
	die('Cannot create lock file');
	exit;
}

function main()
{

	global $Logger;

	$Logger->write([
		'flag' => 'INFO',
		'message' => 'script iniciado'
	]);

	try {

		// SCRIPT PRINCIPAL AQUI

	} catch (Exception $e) {
		$Logger->write([
			'flag' => 'ERROR',
			'message' => $e->getMessage()
		]);
	}

	$Logger->write([
		'flag' => 'INFO',
		'message' => 'script finalizado'
	]);
}
