<?php

// A query abaixo deve retornar uma coluna "login" com os logins que terão o IP desalocado
$queryLogins = "SELECT *
FROM fdwtables.ixc_radusuarios ru
WHERE ru.ip LIKE '%.%.%.%'
	AND (
		ru.ip::inet <<= '*************/24'::inet
		OR ru.ip::inet <<= '***********/24'::inet
		OR ru.ip::inet <<= '*************/24'::inet
		);";

$desalocar_ipv4 = true;
$desalocar_ipv6 = false;

// DEBUG:
$executar_apenas_um = false;

// -------------------------------------------------

require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/Logger.php';

main();

function main() {
	global $queryLogins, $desalocar_ipv4, $desalocar_ipv6, $executar_apenas_um;
	
	$dbh_noc = getConnectionNoc();

	$stmt = $dbh_noc->prepare($queryLogins);
	$stmt->execute();

	$rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

	$logFile = date('Y-m-d_H-i-s') . '_desalocar_ips.txt';

	$i = 1;
	foreach ($rows as $row) {

		$login = $row['login'];

		echo $i++ . ' - ' . $login . PHP_EOL;

		$radusuario = getRadusuario($login);

		$ip_antigo = $radusuario['ip'];

		if ($desalocar_ipv4) {
			$radusuario['fixar_ip'] = 'N';
			$radusuario['ip'] = '';
		}

		if ($desalocar_ipv6) {
			$radusuario['fixar_ipv6'] = 'N';
			$radusuario['framed_fixar_ipv6'] = 'N';
			$radusuario['pd_ipv6'] = '';
			$radusuario['framed_pd_ipv6'] = '';
		}

		updateRadusuario($radusuario);

		$logLine = "$login - $ip_antigo" . PHP_EOL;
		file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);

		if ($executar_apenas_um)
			break;
	}
}