<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/Logger.php';

$ips = [
	'**************'
];

$dates = [
	'2021-01-07 22:44:58',
	'2022-05-07 22:47:56'
];

$outputFile = 'dayanarovaron.csv';

// --------------------------------------------

$csvColumns = [['inicio', 'fim', 'ip', 'login', 'cliente', 'cnpj_cpf', 'logradouro', 'numero', 'bairro', 'cidade']];
$csvData = [];
echo "Inicio: " . date('Y-m-d H:i:s') . PHP_EOL;
foreach($ips as $ip) {
	$radacctData = getRadacct($ip, $date);
	if ($radacctData)
		$csvData = array_merge($csvData, $radacctData);
}

// Ordenação da planilha
usort($csvData, function($a, $b) {
	// Ordena primeiramente por nome do cliente
	$clienteA = $a[4];
	$clienteB = $b[4];
	$spaceShip = ($clienteA <=> $clienteB);
	if($spaceShip != 0)
		return $spaceShip;

	// Ordena segundamente por login do cliente
	$loginA = $a[3];
	$loginB = $b[3];
	$spaceShip = ($loginA <=> $loginB);
	if($spaceShip != 0)
		return $spaceShip;

	// Ordena terceiramente pelo início da conexão
	$startA = strtotime(str_replace('/', '-', $a[0]));
	$startB = strtotime(str_replace('/', '-', $b[0]));	
	return ($startA < $startB) ? -1 : 1;
});

$csvData = array_merge($csvColumns, $csvData);
createCsv($csvData, $outputFile);
echo "Termino: " . date('Y-m-d H:i:s') . PHP_EOL;

// ---------------------------------------------

function getRadacct($ip, $date) {
	$resultados = getRadacctIxc($ip, $date);

	return $resultados;
}

function getRadacctIxc($ip) {
	$api = getIxcApi();
	$params = array(
		'qtype' => 'radacct.framedipaddress',
		'query' => $ip,
		'oper' => '=',
		'sortname' => 'radacct.radacctid',
		'sortorder' => 'asc',
		'page' => '1',
		'rp' => '99000'
	);
	
	$api->get('radacct', $params);
	$retorno = $api->getRespostaConteudo(true);

	$registros = $retorno['registros'];

	$resultados = [];
	foreach($registros as $registro) {
		$acctstarttime = strtotime(str_replace('/', '-', $registro['acctstarttime']));
		$acctstoptime = strtotime(str_replace('/', '-', $registro['acctstoptime']));

		$userData = getUserData($registro['username']);

		$resultados[] = [
			$registro['acctstarttime'],
			$registro['acctstoptime'],
			$registro['framedipaddress'],
			$registro['username'],
			$userData['cliente'],
			$userData['cnpj_cpf'],
			$userData['logradouro'],
			$userData['numero'],
			$userData['bairro'],
			$userData['cidade']
		];
	}

	return $resultados;
}

function getRadacctRadius($ip, $date) {
	$dbh_noc = getConnectionNoc();
	$stmt = $dbh_noc->prepare("SELECT
		TO_CHAR(ra.acctstarttime, 'DD/MM/YYYY HH24:MI:SS') AS inicio,
		TO_CHAR(ra.acctstoptime, 'DD/MM/YYYY HH24:MI:SS') AS fim,
		ra.framedipaddress AS ip,
		ra.username AS login,
		cl.razao AS cliente,
		cl.cnpj_cpf AS cnpj_cpf,
		cl.endereco AS logradouro,
		cl.numero AS numero,
		cl.bairro AS bairro,
		ci.nome AS cidade
	FROM fdwtables.radacct ra
	LEFT JOIN fdwtables.ixc_radusuarios ru ON ru.login = ra.username
	LEFT JOIN fdwtables.ixc_cliente cl ON cl.id = ru.id_cliente
	LEFT JOIN fdwtables.ixc_cidade ci ON ci.id = cl.cidade
	WHERE ra.framedipaddress = ?
		AND
			(ra.acctstarttime <= ? AND ra.acctstoptime >= ?)
	ORDER BY ra.acctstarttime;");

	$stmt->execute([$ip, $date, $date]);

	$resultados = $stmt->fetchAll(PDO::FETCH_NUM);

	return $resultados;
}

function getUserData($login) {
	$dbh_ixc = getConnectionIxc();
	$stmt = $dbh_ixc->prepare("SELECT cl.razao AS cliente,
	cl.cnpj_cpf AS cnpj_cpf,
	cl.endereco AS logradouro,
	cl.numero AS numero,
	cl.bairro AS bairro,
	ci.nome AS cidade
	FROM radusuarios ru
	LEFT JOIN cliente cl ON cl.id = ru.id_cliente
	LEFT JOIN cidade ci ON ci.id = cl.cidade
	WHERE ru.login = ?;");
	$stmt->execute([$login]);

	return $stmt->fetch(PDO::FETCH_ASSOC);
}

function createCsv($array, $outputfile) {
	$fp = fopen($outputfile, 'w');
	foreach ($array as $fields)
		fputcsv($fp, $fields);
	fclose($fp);
}