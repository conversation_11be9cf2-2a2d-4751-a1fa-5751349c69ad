<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/Logger.php';

$outputFile = 'lastmonths.csv';

// --------------------------------------------

$results = getRadacctIxc();

$logins_ativos = [];
foreach($results as $result) {
	$login = $result['cell'][1];
	if(!in_array($login, $logins_ativos))
		$logins_ativos[] = $login;
}
echo "Logins ativos radacct elastic:" . sizeof($logins_ativos) . PHP_EOL;
$logins_ativos_str = implode("','", $logins_ativos);

$dbh_ixc = getConnectionIxc();
$stmt = $dbh_ixc->prepare("SELECT *
FROM radusuarios ru
INNER JOIN cliente_contrato cc ON cc.id = ru.id_contrato
	AND cc.data_cadastro_sistema < '2021-05-01'
	AND cc.status_internet = 'A'
WHERE ru.ativo = 'S'
	AND ru.login NOT IN ('$logins_ativos_str')
	AND ru.login NOT IN (
		SELECT username FROM radacct
	);");
$stmt->execute();

$inativos = $stmt->fetchAll(PDO::FETCH_ASSOC);

foreach($inativos as $inativo) {
	echo $inativo['login'] . PHP_EOL;
}
exit;
// --------------------------------------------

$csvColumns = [['inicio', 'fim', 'ip', 'login', 'cliente', 'cnpj_cpf', 'logradouro', 'numero', 'bairro', 'cidade']];
$csvData = [];
echo "Inicio: " . date('Y-m-d H:i:s') . PHP_EOL;
foreach($ips as $ip) {
	$radacctData = getRadacct($ip, $date);
	if ($radacctData)
		$csvData = array_merge($csvData, $radacctData);
}

// Ordenação da planilha
usort($csvData, function($a, $b) {
	// Ordena primeiramente por nome do cliente
	$clienteA = $a[4];
	$clienteB = $b[4];
	$spaceShip = ($clienteA <=> $clienteB);
	if($spaceShip != 0)
		return $spaceShip;

	// Ordena segundamente por login do cliente
	$loginA = $a[3];
	$loginB = $b[3];
	$spaceShip = ($loginA <=> $loginB);
	if($spaceShip != 0)
		return $spaceShip;

	// Ordena terceiramente pelo início da conexão
	$startA = strtotime(str_replace('/', '-', $a[0]));
	$startB = strtotime(str_replace('/', '-', $b[0]));	
	return ($startA < $startB) ? -1 : 1;
});

$csvData = array_merge($csvColumns, $csvData);
createCsv($csvData, $outputFile);
echo "Termino: " . date('Y-m-d H:i:s') . PHP_EOL;

// ---------------------------------------------

function getRadacct($ip, $date) {
	$resultados = getRadacctIxc($ip, $date);

	return $resultados;
}

function getRadacctIxc() {
	$api = getIxcApi();
	$params = array(
		'qtype' => 'radacct.radacctid',
		'oper' => '>',
		'query' => '19540725',
		'sortname' => 'radacct.radacctid',
		'sortorder' => 'asc',
		'page' => '1',
		'rp' => '99000'
	);
	
	$api->get('radacct', $params);
	$retorno = $api->getRespostaConteudo(true);

	$registros = $retorno['rows'];

	return $registros;
}

function getRadacctRadius($ip, $date) {
	$dbh_noc = getConnectionNoc();
	$stmt = $dbh_noc->prepare("SELECT
		TO_CHAR(ra.acctstarttime, 'DD/MM/YYYY HH24:MI:SS') AS inicio,
		TO_CHAR(ra.acctstoptime, 'DD/MM/YYYY HH24:MI:SS') AS fim,
		ra.framedipaddress AS ip,
		ra.username AS login,
		cl.razao AS cliente,
		cl.cnpj_cpf AS cnpj_cpf,
		cl.endereco AS logradouro,
		cl.numero AS numero,
		cl.bairro AS bairro,
		ci.nome AS cidade
	FROM fdwtables.radacct ra
	LEFT JOIN fdwtables.ixc_radusuarios ru ON ru.login = ra.username
	LEFT JOIN fdwtables.ixc_cliente cl ON cl.id = ru.id_cliente
	LEFT JOIN fdwtables.ixc_cidade ci ON ci.id = cl.cidade
	WHERE ra.framedipaddress = ?
		AND
			(ra.acctstarttime <= ? AND ra.acctstoptime >= ?)
	ORDER BY ra.acctstarttime;");

	$stmt->execute([$ip, $date, $date]);

	$resultados = $stmt->fetchAll(PDO::FETCH_NUM);

	return $resultados;
}

function getUserData($login) {
	$dbh_ixc = getConnectionIxc();
	$stmt = $dbh_ixc->prepare("SELECT cl.razao AS cliente,
	cl.cnpj_cpf AS cnpj_cpf,
	cl.endereco AS logradouro,
	cl.numero AS numero,
	cl.bairro AS bairro,
	ci.nome AS cidade
	FROM radusuarios ru
	LEFT JOIN cliente cl ON cl.id = ru.id_cliente
	LEFT JOIN cidade ci ON ci.id = cl.cidade
	WHERE ru.login = ?;");
	$stmt->execute([$login]);

	return $stmt->fetch(PDO::FETCH_ASSOC);
}

function createCsv($array, $outputfile) {
	$fp = fopen($outputfile, 'w');
	foreach ($array as $fields)
		fputcsv($fp, $fields);
	fclose($fp);
}