<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/utils.php';
require_once __DIR__ . '/../../common/Logger.php';

function main()
{
	$api = getIxcApi();

	$params = array(
		'qtype' => 'radacct.radacctid', //campo de filtro
		'query' => '', //valor para consultar
		'oper' => '=', //operador da consulta
		'sortname' => 'radacct.radacctid', //campo para ordenar a consulta
		'sortorder' => 'desc', //ordenação (asc= crescente | desc=decrescente)
		'page' => '1', //página a ser mostrada

	);
	$api->get('radacct', $params);
	$resposta = $api->getRespostaConteudo(true);// false para json | true para array

	var_dump($resposta);

	// var_dump($resposta); return;

	// var_dump($resposta['registros']);return;

	echo PHP_EOL . count($resposta['registros']) . PHP_EOL;
}

main();
