<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/Logger.php';

$username = 'paulamello';
$inicio = '2020-10-25';
$fim = '2024-10-26';

$outputFile = 'paulamello.csv';

// --------------------------------------------

$csvColumns = [['inicio', 'fim', 'ipv4', 'ipv6', 'login', 'mac', 'cliente', 'cnpj_cpf', 'logradouro', 'numero', 'bairro', 'cidade']];
$csvData = [];
echo "Inicio do script: " . date('Y-m-d H:i:s') . PHP_EOL;
$radacctData = getRadacctByUsername($username, $inicio, $fim);
if ($radacctData)
	$csvData = array_merge($csvData, $radacctData);

// Ordenação da planilha
usort($csvData, function($a, $b) {
	// Ordena em primeiro lugar por nome do cliente
	$clienteA = $a[4];
	$clienteB = $b[4];
	$spaceShip = strcmp($clienteA, $clienteB);
	if($spaceShip != 0)
		return $spaceShip;

	// Ordena em segundo lugar por login do cliente
	$loginA = $a[3];
	$loginB = $b[3];
	$spaceShip = strcmp($loginA, $loginB);
	if($spaceShip != 0)
		return $spaceShip;

	// Ordena em terceiro lugar pelo início da conexão
	$startA = strtotime(str_replace('/', '-', $a[0]));
	$startB = strtotime(str_replace('/', '-', $b[0]));	
	return ($startA < $startB) ? -1 : 1;
});

$csvData = array_merge($csvColumns, $csvData);
createCsv($csvData, $outputFile);
echo "Termino do script: " . date('Y-m-d H:i:s') . PHP_EOL;

// ---------------------------------------------

function getRadacctByUsername($username, $inicio, $fim) {

	$column = 'username';

	$api = getIxcApi();
	$params = array(
		'qtype' => "radacct.$column",
		'query' => $username,
		'oper' => '=',
		'sortname' => 'radacct.radacctid',
		'sortorder' => 'asc',
		'page' => '1',
		'rp' => '99000'
	);
	
	$api->get('radacct', $params);
	$retorno = $api->getRespostaConteudo(true);

	$registros = $retorno['registros'];

	$ts_inicio = strtotime($inicio);
	$ts_fim = strtotime($fim);

	$resultados = [];
	foreach($registros as $registro) {
		$acctstarttime = strtotime(str_replace('/', '-', $registro['acctstarttime']));
		$acctstoptime = strtotime(str_replace('/', '-', $registro['acctstoptime']));

		if(
			!(
			($acctstarttime > $ts_inicio && $acctstarttime < $ts_fim)
			||
			($acctstoptime > $ts_inicio && $acctstoptime < $ts_fim)
			||
			($acctstarttime < $ts_inicio && $acctstoptime > $ts_fim)
			)
		)
			continue;

		$userData = getUserData($registro['username']);

		$resultados[] = [
			$registro['acctstarttime'],
			$registro['acctstoptime'],
			$registro['framedipaddress'],
			$registro['delegatedipv6prefix'],
			$registro['username'],
			$registro['callingstationid'],
			$userData['cliente'],
			$userData['cnpj_cpf'],
			$userData['logradouro'],
			$userData['numero'],
			$userData['bairro'],
			$userData['cidade']
		];
	}

	return $resultados;
}

function getRadacctRadius($ip, $date) {
	$dbh_noc = getConnectionNoc();
	$stmt = $dbh_noc->prepare("SELECT
		TO_CHAR(ra.acctstarttime, 'DD/MM/YYYY HH24:MI:SS') AS inicio,
		TO_CHAR(ra.acctstoptime, 'DD/MM/YYYY HH24:MI:SS') AS fim,
		ra.framedipaddress AS ip,
		ra.username AS login,
		cl.razao AS cliente,
		cl.cnpj_cpf AS cnpj_cpf,
		cl.endereco AS logradouro,
		cl.numero AS numero,
		cl.bairro AS bairro,
		ci.nome AS cidade
	FROM fdwtables.radacct ra
	LEFT JOIN fdwtables.ixc_radusuarios ru ON ru.login = ra.username
	LEFT JOIN fdwtables.ixc_cliente cl ON cl.id = ru.id_cliente
	LEFT JOIN fdwtables.ixc_cidade ci ON ci.id = cl.cidade
	WHERE ra.framedipaddress = ?
		AND
			(ra.acctstarttime <= ? AND ra.acctstoptime >= ?)
	ORDER BY ra.acctstarttime;");

	$stmt->execute([$ip, $date, $date]);

	$resultados = $stmt->fetchAll(PDO::FETCH_NUM);

	return $resultados;
}

function getUserData($login) {
	$dbh_ixc = getConnectionIxc();
	$stmt = $dbh_ixc->prepare("SELECT cl.razao AS cliente,
	cl.cnpj_cpf AS cnpj_cpf,
	cl.endereco AS logradouro,
	cl.numero AS numero,
	cl.bairro AS bairro,
	ci.nome AS cidade
	FROM radusuarios ru
	LEFT JOIN cliente cl ON cl.id = ru.id_cliente
	LEFT JOIN cidade ci ON ci.id = cl.cidade
	WHERE ru.login = ?;");
	$stmt->execute([$login]);

	return $stmt->fetch(PDO::FETCH_ASSOC);
}

function createCsv($array, $outputfile) {
	$fp = fopen($outputfile, 'w');
	foreach ($array as $fields)
		fputcsv($fp, $fields);
	fclose($fp);
}