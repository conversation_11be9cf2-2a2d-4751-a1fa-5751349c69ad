<?php
// Data de expiração do filtro/grupo de e-mails
$expirationDate = '2023-12-31 23:59:59';

// Para enviar para todos os clientes ativos
// <PERSON><PERSON>o seja "false", o script irá utilizar o resultado da query "$sql"
$todosClientesAtivos = true;

// Seleciona os IDs dos contratos para criar o filtro/grupo de e-mails
// A query precisa retornar uma coluna com o id do contrato, o nome da coluna deve ser "id"
$sql = "SELECT cc.id FROM cliente_contrato cc WHERE cc.status NOT IN ('D', 'I');";

// Quantidade máxima de destinatários por grupo
$groupMaxSize = 16000;

// -------------------------------------------------------------------------------

require_once __DIR__ . '/../../common/config.php';

main();

function main()
{
	global $todosClientesAtivos;
	global $sql;
	global $expirationDate;
	global $groupMaxSize;

	if ($todosClientesAtivos) {
		$sql = "SELECT cc.id FROM cliente_contrato cc WHERE cc.status NOT IN ('D', 'I');";
	}

	$dbh_noc = getConnectionNoc();
	$dbh_ixc = getConnectionIxc();	
	
	$stmt = $dbh_ixc->prepare($sql);
	$stmt->execute();

	$ids_contratos = [];
	while($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
		$ids_contratos[] = $row['id'];
	}
	$ids_contratos_str = implode(', ', $ids_contratos);

	$noc_sql = "SELECT email FROM public.mailing WHERE id_contrato IN ($ids_contratos_str);";
	
	$stmt = $dbh_noc->prepare($noc_sql);
	$stmt->execute();

	$result = $stmt->fetchAll(PDO::FETCH_ASSOC);

	$emailsAdicionados = [];
	foreach($result as $row) {
		if (strpos($row['email'], ',') !== false) {
			$emails = explode(',', $row['email']);
			foreach($emails as $email) {
				$emailsAdicionados[] = trim($email);
			}
		}
		else if (strpos($row['email'], ';') !== false) {
			$emails = explode(';', $row['email']);
			foreach($emails as $email) {
				$emailsAdicionados[] = trim($email);
			}
		}
		else {
			$emailsAdicionados[] = trim($row['email']);
		}
	}

	$emails_a_enviar = [];
	foreach ($emailsAdicionados as $endereco) {
		if(trim($endereco) === '')
			continue;

		$a_adicionar = trim($endereco);
		if(!in_array($a_adicionar, $emails_a_enviar))
			$emails_a_enviar[] = $a_adicionar;
	}

	$grupos = array_chunk($emails_a_enviar, $groupMaxSize);

	foreach ($grupos as $grupo) {
		$qtdeDestinatarios = sizeof($grupo);
		$aliasAddress = getAliasAddress();

		$values = '';		
		foreach ($grupo as $endereco) {
			$values .= "('$aliasAddress', '$endereco', 1, 1, '$expirationDate'), ";
		}
		$values = rtrim($values, ', ');
	
		if (insertAlias($values)) {
			echo "Grupo criado com sucesso:";
		}
		else {
			echo "Erro ao criar o grupo:";
		}

		echo PHP_EOL . $aliasAddress . PHP_EOL . "$qtdeDestinatarios destinatários" . PHP_EOL . PHP_EOL;
	}

	echo "Data de expiração dos grupos: $expirationDate" . PHP_EOL . PHP_EOL;
}

function insertAlias($values) {
	$sql = "INSERT INTO alias (address, goto, active, dynamic, expiration) VALUES $values;";
	$dbh_icewarp = getConnectionIcewarp();
	$stmt = $dbh_icewarp->prepare($sql);

	return $stmt->execute();
}

function getAliasAddress() {
	$aliasAddress = mt_rand() . "@pocos-net.com.br";

	// Check if alias already exists
	$dbh_icewarp = getConnectionIcewarp();
	$stmt = $dbh_icewarp->prepare("SELECT * FROM alias WHERE address = ?;");
	$stmt->execute([$aliasAddress]);

	if ($stmt->rowCount() == 0) {
		return $aliasAddress;
	}
	else {
		return getAliasAddress();
	}
}