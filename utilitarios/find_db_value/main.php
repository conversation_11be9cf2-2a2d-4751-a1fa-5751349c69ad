<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/Logger.php';

// -------------------------------------------------------
// Texto a ser buscado
$searchText = '<EMAIL>';

$pdo = getConnectionIxc();

try {
    // Obter todas as tabelas do banco de dados
    $tablesQuery = $pdo->query("SHOW TABLES");
    $tables = $tablesQuery->fetchAll(PDO::FETCH_COLUMN);

    // echo "Iniciando busca por '$searchText' no banco de dados '$dbName'...\n";

    foreach ($tables as $table) {
        if (strpos($table, 'log') !== false) {
            continue;
        }
        echo "\rVerificando tabela: $table";

        // Obter todas as colunas da tabela atual
        $columnsQuery = $pdo->query("SHOW COLUMNS FROM `$table`");
        $columns = $columnsQuery->fetchAll(PDO::FETCH_ASSOC);

        $searchColumns = [];

        // Filtrar colunas por tipo (VARCHAR, TEXT, CHAR, etc.)
        foreach ($columns as $column) {
            $type = strtoupper($column['Type']);
            if (
                strpos($type, 'VARCHAR') !== false ||
                strpos($type, 'TEXT') !== false ||
                strpos($type, 'CHAR') !== false ||
                strpos($type, 'BLOB') !== false
            ) {
                $searchColumns[] = $column['Field'];
            }
        }

        if (empty($searchColumns)) {
            // echo "Nenhuma coluna de texto encontrada nesta tabela.\n";
            continue;
        }

        // Construir a query de busca para a tabela atual
        $whereClauses = [];
        foreach ($searchColumns as $col) {
            $whereClauses[] = "`$col` = '$searchText'";
        }
        $where = implode(' OR ', $whereClauses);

        $query = "SELECT * FROM `$table` WHERE $where";
        $stmt = $pdo->prepare($query);
        //$stmt->bindValue(':searchText', "%$searchText%", PDO::PARAM_STR);
        $stmt->execute();

        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (count($results) > 0) {
            echo "Encontrado(s) " . count($results) . " registro(s) na tabela '$table':\n";

            foreach ($results as $row) {
                echo "  - ID/Chave: " . json_encode(array_intersect_key($row, array_flip($searchColumns))) . "\n";
                // Opcional: descomente para mostrar todo o registro
                // echo "    Registro completo: " . json_encode($row) . "\n";
            }
        }
    }

    echo "\nBusca concluída.\n";
} catch (PDOException $e) {
    die("Erro no banco de dados: " . $e->getMessage());
}
