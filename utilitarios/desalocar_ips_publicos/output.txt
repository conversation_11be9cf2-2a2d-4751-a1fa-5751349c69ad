array(4) {
  ["login"]=>
  string(14) "juverlanvieira"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(15) "***************"
  ["novo_ip"]=>
  string(15) "***************"
}
Update juverlanvieira:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "18584"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "29124"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(18) "henrique.valadares"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(15) "***************"
}
Update henrique.valadares:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "20438"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "33298"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(11) "thais.bruno"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(15) "***************"
  ["novo_ip"]=>
  string(15) "***************"
}
Update thais.bruno:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "22393"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "36808"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(7) "antonia"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(15) "***************"
}
Update antonia:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "22948"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "38113"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(7) "d.salim"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(15) "**************9"
}
Update d.salim:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "23326"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "39060"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(14) "mauricio.flora"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(15) "**************4"
}
Update mauricio.flora:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "23563"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "39573"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(13) "a.florenciano"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(15) "**************1"
}
Update a.florenciano:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "23639"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "39724"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(6) "mebras"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(15) "***************"
}
Update mebras:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "23981"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "40274"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(13) "jaques.pontes"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(15) "***************"
}
Update jaques.pontes:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "24266"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "40716"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(12) "vitor.abdala"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(15) "***************"
}
Update vitor.abdala:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "24500"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "36194"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(6) "hermes"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(15) "**************9"
}
Update hermes:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "24606"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "41130"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(12) "elisangela.u"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(15) "**************6"
}
Update elisangela.u:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "24647"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "41190"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(10) "ace.pinhal"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(15) "**************3"
}
Update ace.pinhal:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "24681"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "41232"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(9) "amanda.cm"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(15) "**************1"
}
Update amanda.cm:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "24727"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "41299"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(6) "tacila"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(15) "*************14"
  ["novo_ip"]=>
  string(15) "***************"
}
Update tacila:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "24810"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "41395"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(7) "edson.m"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(15) "*************17"
  ["novo_ip"]=>
  string(15) "***************"
}
Update edson.m:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "24911"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "41511"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(6) "alex.a"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(15) "**************9"
}
Update alex.a:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "24920"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "41518"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(7) "julio.f"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "*************0"
  ["novo_ip"]=>
  string(15) "**************8"
}
Update julio.f:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "25074"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "41697"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(13) "joao.raimundo"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "*************5"
  ["novo_ip"]=>
  string(15) "**************5"
}
Update joao.raimundo:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "25296"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "41977"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(7) "ro.rosa"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "*************0"
  ["novo_ip"]=>
  string(15) "***************"
}
Update ro.rosa:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "25591"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "42327"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(0) ""
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(1) "0"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(11) "selmalima18"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(15) "***************"
}
Update selmalima18:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "25619"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "42363"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(13) "flavia.regina"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(15) "***************"
}
Update flavia.regina:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "25758"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "42532"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(5) "ana.r"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "*************1"
  ["novo_ip"]=>
  string(15) "***************"
}
Update ana.r:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "25927"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "42729"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(7) "cesar.v"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(15) "***************"
}
Update cesar.v:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "25939"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "42742"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(8) "tamara.b"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(15) "***************"
}
Update tamara.b:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "25954"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "42764"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(6) "hegnes"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(15) "***************"
}
Update hegnes:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "26485"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "43486"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(14) "marcelo.santos"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(15) "**************8"
}
Update marcelo.santos:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "26711"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "43764"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(7) "lindsey"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "*************9"
  ["novo_ip"]=>
  string(15) "***************"
}
Update lindsey:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "26847"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "43928"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(7) "david.j"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "*************4"
  ["novo_ip"]=>
  string(15) "***************"
}
Update david.j:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "26983"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "44082"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(6) "mineia"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(15) "*************26"
  ["novo_ip"]=>
  string(15) "***************"
}
Update mineia:array(6) {
  ["cliente_coord"]=>
  string(7) "success"
  ["message_cliente_coord"]=>
  string(134) "As coordenadas do Cliente 44259 - MINEIA ANGELOTI SEMPREBONE PARADELA foram alteradas para serem equivalentes as coordenadas do Login."
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "27119"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "44259"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(10) "rodrigo.rn"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(15) "***************"
}
Update rodrigo.rn:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "27154"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "44304"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(6) "elthon"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(15) "***************"
}
Update elthon:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "27305"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "39228"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(10) "valeria.ab"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(15) "***************"
}
Update valeria.ab:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "27329"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "44490"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(8) "thayna.f"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(15) "***************"
  ["novo_ip"]=>
  string(15) "***************"
}
Update thayna.f:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "27506"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "44659"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(9) "silveirap"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(15) "***************"
}
Update silveirap:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "27763"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "44941"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(8) "fatima.l"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(15) "***************"
}
Update fatima.l:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "27858"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "42500"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(16) "rodrigo.honorato"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(15) "***************"
}
Update rodrigo.honorato:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "28060"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "45259"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(7) "enedino"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(15) "***************"
}
Update enedino:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "28084"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "45284"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(11) "jefferson.d"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(15) "***************"
}
Update jefferson.d:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "28094"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "45296"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(9) "maria.ccl"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(15) "***************"
}
Update maria.ccl:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "28102"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "45306"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(14) "antonio.garcia"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(15) "***************"
  ["novo_ip"]=>
  string(15) "***************"
}
Update antonio.garcia:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "28340"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "45592"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(13) "daiane.santos"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(15) "***************"
}
Update daiane.santos:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "28401"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "45661"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(8) "diego.gm"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(15) "***************"
  ["novo_ip"]=>
  string(15) "***************"
}
Update diego.gm:array(6) {
  ["cliente_coord"]=>
  string(7) "success"
  ["message_cliente_coord"]=>
  string(120) "As coordenadas do Cliente 45905 - DIEGO GABRIEL MACHADO foram alteradas para serem equivalentes as coordenadas do Login."
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "28607"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "45905"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(8) "airton.r"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(13) "*************"
  ["novo_ip"]=>
  string(15) "**************9"
}
Update airton.r:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "28842"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "46167"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(15) "alexandre.costa"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(15) "**************7"
}
Update alexandre.costa:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "29008"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "28823"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(8) "leonel.r"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(15) "*************11"
  ["novo_ip"]=>
  string(15) "**************4"
}
Update leonel.r:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "29267"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "46649"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(12) "danila.rocha"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(15) "*************19"
  ["novo_ip"]=>
  string(15) "**************3"
}
Update danila.rocha:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "29428"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "46833"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(9) "daniel.ec"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(15) "*************20"
  ["novo_ip"]=>
  string(15) "**************0"
}
Update daniel.ec:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "29462"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "46878"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(7) "luisfef"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(15) "*************15"
  ["novo_ip"]=>
  string(15) "***************"
}
Update luisfef:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "29707"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "47139"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(14) "felipe.eduardo"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(15) "***************"
}
Update felipe.eduardo:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "29764"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "47204"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(7) "marco.o"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(15) "***************"
}
Update marco.o:array(6) {
  ["cliente_coord"]=>
  string(7) "success"
  ["message_cliente_coord"]=>
  string(130) "As coordenadas do Cliente 47287 - MARCO ANTONIO ROCHA DE OLIVEIRA foram alteradas para serem equivalentes as coordenadas do Login."
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "29845"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "47287"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(7) "ivani.a"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(14) "*************9"
}
Update ivani.a:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "30724"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "48240"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(14) "sueliaparecida"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(15) "***************"
  ["novo_ip"]=>
  string(14) "*************8"
}
Update sueliaparecida:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "30779"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "43812"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(6) "renanc"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(13) "*************"
  ["novo_ip"]=>
  string(14) "*************7"
}
Update renanc:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "31002"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "48494"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(7) "celio.t"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(15) "*************02"
  ["novo_ip"]=>
  string(14) "*************3"
}
Update celio.t:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "32287"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "49856"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(15) "rafael.silveira"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(14) "*************0"
}
Update rafael.silveira:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "33246"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "50825"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(8) "paolla.s"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(14) "**************"
}
Update paolla.s:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "33545"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "51114"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(8) "renatama"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(15) "*************21"
  ["novo_ip"]=>
  string(14) "**************"
}
Update renatama:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "33760"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "37729"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(11) "elias.silva"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(14) "**************"
}
Update elias.silva:array(6) {
  ["cliente_coord"]=>
  string(7) "success"
  ["message_cliente_coord"]=>
  string(119) "As coordenadas do Cliente 51563 - ELIAS ALVES DA SILVA foram alteradas para serem equivalentes as coordenadas do Login."
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "34004"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "51563"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(11) "maria.nilse"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(14) "**************"
}
Update maria.nilse:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "34162"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "51703"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(11) "m.menegatto"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(15) "*************10"
  ["novo_ip"]=>
  string(14) "*************8"
}
Update m.menegatto:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "34278"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "51812"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(8) "marcio.t"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(14) "*************7"
}
Update marcio.t:array(6) {
  ["cliente_coord"]=>
  string(7) "success"
  ["message_cliente_coord"]=>
  string(112) "As coordenadas do Cliente 51887 - MARCIO TOBIAS foram alteradas para serem equivalentes as coordenadas do Login."
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "34353"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "51887"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(5) "hunza"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(14) "*************5"
}
Update hunza:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "34667"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "52164"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(8) "paulo.se"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(14) "*************4"
}
Update paulo.se:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "34696"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(4) "3043"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(8) "l.salles"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(14) "*************2"
}
Update l.salles:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "34749"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "52233"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(7) "sfatima"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(14) "*************1"
}
Update sfatima:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "34815"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "52286"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(5) "emisc"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(14) "*************7"
}
Update emisc:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "35710"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "53119"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(8) "rebecacc"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(14) "*************5"
}
Update rebecacc:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "36243"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "33230"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(16) "maristela.rangel"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(15) "*************27"
  ["novo_ip"]=>
  string(14) "*************4"
}
Update maristela.rangel:array(6) {
  ["cliente_coord"]=>
  string(7) "success"
  ["message_cliente_coord"]=>
  string(115) "As coordenadas do Cliente 53942 - MARISTELA RANGEL foram alteradas para serem equivalentes as coordenadas do Login."
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "36725"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "53942"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(10) "lecio.jose"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(14) "*************3"
}
Update lecio.jose:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "36949"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "54104"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(15) "maurilio.franca"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(14) "**************"
}
Update maurilio.franca:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "36968"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "54121"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(11) "pauloalbina"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(14) "**************"
}
Update pauloalbina:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "37149"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "37506"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(0) ""
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(1) "0"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(14) "tereza.sartini"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(14) "**************"
}
Update tereza.sartini:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "37290"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "54353"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(9) "camilly.m"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(14) "**************"
}
Update camilly.m:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "37522"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "54535"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(0) ""
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(1) "0"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(11) "jose.amadeu"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(14) "**************"
}
Update jose.amadeu:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "37619"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "41633"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(9) "maria.csb"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(14) "**************"
}
Update maria.csb:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "37650"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "54638"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(8) "adilsonc"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(15) "***************"
  ["novo_ip"]=>
  string(14) "**************"
}
Update adilsonc:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "37738"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "54712"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(6) "celiar"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(14) "**************"
}
Update celiar:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "37813"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "54769"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(8) "armandob"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(13) "*************"
}
Update armandob:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "37866"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "54812"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(13) "lidia.sampaio"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(13) "*************"
}
Update lidia.sampaio:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "38171"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "55035"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(8) "del.beli"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(14) "**************"
  ["novo_ip"]=>
  string(13) "*************"
}
Update del.beli:array(4) {
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "38222"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "55066"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
array(4) {
  ["login"]=>
  string(11) "lucio.vitor"
  ["pool"]=>
  string(21) "PPPOE Esp Martorano_1"
  ["ip_atual"]=>
  string(15) "***************"
  ["novo_ip"]=>
  string(13) "*************"
}
Update lucio.vitor:array(6) {
  ["cliente_coord"]=>
  string(7) "success"
  ["message_cliente_coord"]=>
  string(118) "As coordenadas do Cliente 42623 - LUCIO VITOR OLIVIER foram alteradas para serem equivalentes as coordenadas do Login."
  ["type"]=>
  string(7) "success"
  ["message"]=>
  string(32) "Registro atualizado com sucesso!"
  ["id"]=>
  string(5) "39118"
  ["atualiza_campos"]=>
  array(4) {
    [0]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "id_cliente"
      ["valor"]=>
      string(5) "42623"
    }
    [1]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(10) "porta_http"
      ["valor"]=>
      string(2) "80"
    }
    [2]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(13) "porta_router2"
      ["valor"]=>
      string(4) "8091"
    }
    [3]=>
    array(3) {
      ["tipo"]=>
      string(1) "i"
      ["campo"]=>
      string(16) "ssid_router_wifi"
      ["valor"]=>
      string(0) ""
    }
  }
}
