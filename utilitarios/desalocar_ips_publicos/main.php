<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/Logger.php';

$range_a_desalocar = '*************/25';

main();

function main() {
	global $range_a_desalocar;

	$api = getIxcApi();
	$dbh_ixc = getConnectionIxc();
	$dbh_noc = getConnectionNoc();

	$stmt = $dbh_noc->prepare("SELECT ru.login, ru.ip
		FROM fdwtables.ixc_radusuarios ru
		WHERE ru.ip LIKE '%.%.%.%'
			AND ru.ip::inet <<= '$range_a_desalocar'::inet;");
	$stmt->execute();

	$rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

	$i = 0 ;
	foreach ($rows as $row) {
		// Limitador para testes:
		// if($i++ == 1)
		// 	break;
		$login = $row['login'];

		$radusuario = getRadusuario($login);

		$transmissor = $radusuario['transmissor'];
		$cidade = getCidadeByRadusuario($radusuario);

		$pool_name = get_pool_name($transmissor, $cidade);
		$free_ip = get_free_public_ip($pool_name);

		$data = [
			"login" => $login,
			"pool" => $pool_name,
			"ip_atual" => $radusuario['ip'],
			"novo_ip" => $free_ip
		];

		$radusuario['ip'] = $free_ip;
		$update = updateRadusuario($radusuario);

		var_dump($data);
		echo "Update $login:";
		var_dump($update);
	}
}


function get_free_public_ip($pool_name)
{
	$dbh = getConnectionNoc();

	$free_ip = null;

	$stmt = $dbh->prepare("SELECT rp.framedipaddress
		FROM fdwtables.radippool rp
		INNER JOIN fdwtables.cad_prefix cp ON cp.prefix = rp.prefix
		WHERE rp.pool_name = ?
		AND host(rp.framedipaddress) NOT IN
      (SELECT ip FROM fdwtables.ixc_radusuarios WHERE ip != '' AND ip IS NOT NULL AND ip ~ '[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}')
		AND NOT cp.cgnat
		AND NOT rp.framedipaddress << ANY (SELECT range FROM network.not_allocated_ranges)
		ORDER BY rp.framedipaddress DESC LIMIT 1;");
	$query = $stmt->execute(array($pool_name));

	if ($query && $stmt->rowCount() > 0) {
		$free_ip = $stmt->fetchColumn();
	}

	return $free_ip;
}

function get_pool_name($transmissor, $cidade)
{
	$dbh = getConnectionNoc();

	$pool_name = null;

	// if ($transmissor && trim($transmissor) != '') {
	// 	$where_pool_query = "UPPER(service) = UPPER(?)";
	// 	$params_pool_query = array($transmissor);
	// } else
	if ($cidade !== 0) {
		switch (mb_strtoupper($cidade)) {
			case 'CALDAS':
			case 'ÁGUAS DA PRATA':
			case 'POÇOS DE CALDAS':
				$city_code = 'Pcs';
				break;
			case 'ANDRADAS':
				$city_code = 'And';
				break;
			case 'ESPÍRITO SANTO DO PINHAL':
				$city_code = 'Esp';
				break;
			case 'SANTO ANTÔNIO DO JARDIM':
				$city_code = 'Jrd';
				break;
			case 'CAMPESTRE':
				$city_code = 'Cps';
				break;
			case 'SÃO JOÃO DA BOA VISTA':
				$city_code = 'Sjb';
				break;
		}
		$where_pool_query = "UPPER(pppoeserver) LIKE UPPER(?)";
		$params_pool_query = array('%' . $city_code . '%');
	}

	$stmt = $dbh->prepare("SELECT pppoeserver FROM fdwtables.cad_services WHERE $where_pool_query;");
	$query = $stmt->execute($params_pool_query);

	if ($query && $stmt->rowCount() > 0) {
		$row = $stmt->fetch(PDO::FETCH_ASSOC);
		$pool_name = $row['pppoeserver'];
	}

	return $pool_name;
}