<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/Logger.php';

$f = fopen(__DIR__ . '/lock', 'w') or die('Cannot create lock file');
if (flock($f, LOCK_EX | LOCK_NB)) {
	main();
}

function main() {
	$dbh = getConnectionIxc();

	$sql = "SELECT * FROM radusuarios WHERE ip LIKE '100.%.0';";
	$stmt = $dbh->prepare($sql);
	$stmt->execute();

	$radusuarios = $stmt->fetchAll(PDO::FETCH_ASSOC);

	$i = 1;
	foreach($radusuarios as $radusuario) {
		echo $i++ . " - removendo IP do usuario $radusuario[login]" . PHP_EOL;

		$radusuario['ip'] = '';
		// $radusuario['pd_ipv6'] = '';
		// $radusuario['framed_pd_ipv6'] = '';

		$radusuario['fixar_ip'] = 'N';
		// $radusuario['fixar_ipv6'] = 'N';
		// $radusuario['framed_fixar_ipv6'] = 'N';

		updateRadusuario($radusuario);
	}
}