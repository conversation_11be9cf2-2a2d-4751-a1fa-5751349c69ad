<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/utils.php';
require_once __DIR__ . '/../../common/Logger.php';

function main()
{
	$dbh_ixc = getConnectionIXC();

	// Lista LOGs de exclusão de logins (no log consta apenas o ID do login excluido, nao o login)
	$stmt = $dbh_ixc->prepare("SELECT * FROM radusuarios WHERE login = ?;");

	$stmt->execute(['decio.j']);
	$radusuario = $stmt->fetch(PDO::FETCH_ASSOC);
	
	$radusuario['login_simultaneo'] = 0;
	$resposta = updateRadusuario($radusuario);

	var_dump($resposta);
}

main();