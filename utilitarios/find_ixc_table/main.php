<?php
require_once __DIR__ . '/../../common/config.php';

// Para encontrar em qual tabela um registro é armazenado
// Informar o ID do registro em "id"
// Informar qualquer outro campo do registro em "needle" (preferencialmente que seja um campo com uma informação não muito comum)

main([
	'id' => 191,
	'needle' => '100.108.153.0/24'
]);

// ---------------------------------------------------------

function getIgnoredTables() {
	return [];
	return ['nfse_xml_pre_consulta', 'parametros', 'produtos_cod_servicos', 'crm_agenda'];
}

function main($options) {
	$ignoredTables = getIgnoredTables();
	$id = $options['id'];
	$needle = $options['needle'];

	$api = getIxcApi();

	$dbh = getConnectionIxc();

	$stmt = $dbh->prepare("SELECT table_name
		FROM information_schema.tables
		WHERE table_type = 'BASE TABLE'
				AND table_schema = DATABASE() 
		ORDER BY table_name;");

	$stmt->execute();

	$tables = $stmt->fetchAll(PDO::FETCH_ASSOC);

	foreach($tables as $table) {
		$table_name = $table['table_name'];
		if(in_array($table_name, $ignoredTables))
			continue;

		$stmt = $dbh->prepare("SELECT * FROM information_schema.columns
			WHERE table_schema = DATABASE()
			AND table_name = ?
			AND column_name = ?;");
		$stmt->execute([$table_name, 'id']);

		if($stmt->rowCount() <= 0)
			continue;
		
		$stmt = $dbh->prepare("SELECT *
		FROM $table_name
		WHERE id = $id;");

		$query = $stmt->execute();

		if($query && $stmt->rowCount() <= 0)
			continue;

		$result = $stmt->fetch(PDO::FETCH_ASSOC);

		$colunas = [];
		foreach($result as $coluna=>$valor) {
			if($valor == $needle) {
				$colunas[] = $coluna;
			}
		}
		if(sizeof($colunas) > 0) {
			echo PHP_EOL . "Tabela: \t$table_name" . PHP_EOL
			. "Coluna(s): \t" . implode($colunas, ', ') . PHP_EOL . PHP_EOL;
		}
	}
}
