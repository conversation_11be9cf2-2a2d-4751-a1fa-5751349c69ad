<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/Logger.php';

main();

function main() {
	$dbh_noc = getConnectionNoc();

	$stmt = $dbh_noc->prepare("SELECT
			login
		FROM fdwtables.ixc_radusuarios 
		WHERE framed_pd_ipv6 != ''
			AND ativo = 'S'
			AND (
				NULLIF(framed_pd_ipv6, '')::inet <<= '2804:3e50::/34'::inet
				OR NULLIF(framed_pd_ipv6, '')::inet <<= '2804:3e50:8000::/36'::inet
				OR NULLIF(framed_pd_ipv6, '')::inet <<= '2804:3e50:a000::/36'::inet
				);");
	$stmt->execute();

	$rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

	$i = 1;
	foreach ($rows as $row) {

		$login = $row['login'];

		echo $i++ . ' - ' . $login . PHP_EOL;

		$radusuario = getRadusuario($login);

		$radusuario['fixar_ipv6'] = 'N';
		$radusuario['framed_fixar_ipv6'] = 'N';
		$radusuario['pd_ipv6'] = '';
		$radusuario['framed_pd_ipv6'] = '';

		$radusuario['mac'] = format_mac($radusuario['mac']);

		updateRadusuario($radusuario);
	}
}

function format_mac($numbers, $delimiter = ':')
{
	// Remover todos os caracteres não hexadecimais (0-9, a-f, A-F)
	$cleaned_numbers = strtoupper(preg_replace('/[^0-9a-fA-F]/', '', $numbers));

	// Verificar se a entrada limpa tem exatamente 12 caracteres hexadecimais
	if (strlen($cleaned_numbers) === 12) {
		// Dividir a string em pares de dois caracteres e juntá-los com o delimitador
		return implode($delimiter, str_split($cleaned_numbers, 2));
	} else {
		// Se não tiver 12 caracteres hexadecimais, retorne a string inalterada
		return $numbers;
	}
}