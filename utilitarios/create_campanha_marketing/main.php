<?php
$campanha = [
    "descricao" => "Campanha para renovação de fidelidade, ganhando 50 MEGA de velocidade adicional",
    "token" => "50m",

    "solicitante" => "Setor Comercial",

    "data_disparo" => "2023-09-13 14:00:00",

    "mensagem_sms" => "PRESENTÃO NA SEMANA DO CLIENTE TELEMIDIA! Participe e ganhe 50 MEGA de velocidade na sua internet clicando aqui: https://promocao.telemidia.net.br/?t=50m",

    "mensagem_fixada_whatsapp" => "Olá, quero participar e turbinar minha internet com 50 MEGA GRÁTIS!",

    // Seleciona os IDs dos contratos para criar o filtro
	// Qualquer query que retorne ID do contrato com nome da coluna "id", vai funcionar
    "query_contratos_ixc" => "SELECT
            cc.id
        FROM cliente_contrato cc
        LEFT JOIN (
            SELECT DISTINCT cc.id
            FROM cliente_contrato cc
            INNER JOIN vd_contratos_produtos vcp ON vcp.id_vd_contrato = cc.id_vd_contrato
            WHERE UPPER(vcp.descricao) LIKE '%DEDICADO%'
            UNION
            SELECT DISTINCT cc.id
            FROM cliente_contrato cc
            INNER JOIN vd_contratos_produtos vcp ON vcp.id_contrato = cc.id
            WHERE UPPER(vcp.descricao) LIKE '%DEDICADO%'
        ) ded ON ded.id = cc.id
        INNER JOIN radusuarios ru ON ru.id_contrato = cc.id
            AND ru.tipo_conexao_mapa = 'F'
        INNER JOIN radgrupos rg ON rg.id = ru.id_grupo
            AND rg.grupo NOT LIKE '%CDM%' AND rg.grupo NOT LIKE '%RADIO%'
        WHERE
            cc.status NOT IN ('D', 'I')
            AND ded.id IS NULL
            AND cc.data_ativacao < '2020-09-15'
            AND cc.data_ativacao IS NOT NULL
            AND cc.data_ativacao != ''
            AND cc.data_expiracao <= '2023-09-15'
            AND cc.data_expiracao IS NOT NULL
            AND cc.data_expiracao != ''
        ORDER BY cc.id ASC;"

    // DEBUG
    // "query_contratos_ixc" => "SELECT
    //         cc.id
    //         FROM cliente_contrato cc
    //     WHERE id = 65491;"
];

// --------------------------------------------------

require_once __DIR__ . '/../../common/config.php';

function main(){
	$dbh_noc = getConnectionNoc();
	$dbh_ixc = getConnectionIxc();
	$dbh_marketing = getConnectionMarketing();

    global $campanha;

    $sql = "INSERT INTO public.campanhas_sms (descricao, token, solicitante, mensagem_sms, mensagem_fixada_whatsapp, data_disparo, query_contratos_ixc, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id;";
    $stmt = $dbh_marketing->prepare($sql);
    $stmt->execute([
        $campanha['descricao'],
        $campanha['token'],
        $campanha['solicitante'],
        $campanha['mensagem_sms'],
        $campanha['mensagem_fixada_whatsapp'],
        $campanha['data_disparo'],
        $campanha['query_contratos_ixc'],
        'PENDENTE'
    ]);
    $id_campanha = $stmt->fetchColumn();

	$stmt = $dbh_ixc->prepare($campanha['query_contratos_ixc']);
	$stmt->execute();
	$ids_contratos = [];
	while($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
		$ids_contratos[] = $row['id'];
	}
	$ids_contratos_str = implode(', ', $ids_contratos);

	$noc_sql = "SELECT *
    FROM public.clientes_telefones_sms
    WHERE id_contrato IN ($ids_contratos_str)
        AND telefone_sms IS NOT NULL;";
	$stmt = $dbh_noc->prepare($noc_sql);
	$stmt->execute();
	$sms_rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

	$values = '';
	foreach ($sms_rows as $row) {
		$values .= "($id_campanha, '$row[telefone_sms]', $row[id_cliente], $row[id_contrato], '$row[cliente]', '$row[cidade]', '$campanha[mensagem_sms]', '$campanha[data_disparo]', 'PENDENTE'), ";
	}
	$values = rtrim($values, ', ');	

	$sql = "INSERT INTO sms_destinatarios (id_campanha, celular, id_cliente, id_contrato, cliente, cidade, mensagem, horario_programado, status) VALUES $values;";
	$stmt = $dbh_marketing->prepare($sql);
	$stmt->execute();

    $quantidade_contratos = sizeof($ids_contratos);
    $quantidade_sms = sizeof($sms_rows);
    $stmt = $dbh_marketing->prepare("UPDATE campanhas_sms SET quantidade_contratos = ?, quantidade_sms = ? WHERE id = ?;");
    $stmt->execute([
        $quantidade_contratos,
        $quantidade_sms,
        $id_campanha
    ]);
}

main();