<?php

require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/utils.php';
require_once __DIR__ . '/../../common/Logger.php';

$api = getIxcApi();

$logins = ['jjsolucoes.limpeza',
'gilda.fernandes',
'nathan.lima',
'otavioh.silva',
'paulo.aa',
'jeanty.junior',
'tatiane.rosa',
'maria2024carvalho',
'amadeu.oliveira',
'oliveira.gustavo',
'lima.lucas',
'selislei.cassia',
'jussara.cristina',
'claudete.isaias',
'paulo.bega',
'lhysandrhaanita',
'alexsander.mage',
'tiago.silva',
'maykel.monteiro',
'nathalia.reis',
'distribuidora.ltda',
'ana.chiodeto',
'thaism.silva',
'stivanin.maria',
'aparecida.costa',
'mariad.santos',
'wesley.geraldo',
'guilherme.sacardo',
'joaop.cardoso',
'nayara.oliveira',
'isadora2024rezende',
'campos.maiara',
'edivaldo2024silva',
'yasmind.fernandes',
'daiane.ldomiciano',
'natanael.andrian',
'josem.filho',
'silva.quiteria',
'mariana2024.soares',
'marcosp.campese'];

foreach($logins as $login) {
    $params = array(
        'login' => $login
    );
    
    $api->get('desconectar_clientes', $params);
    $retorno = $api->getRespostaConteudo(true);
    if(isset($retorno['message']) && strpos($retorno['message'], 'sucesso') !== false) {
        echo "Login desconectado com sucesso: " . PHP_EOL;
    }
    else {
        echo "Erro ao desconectar o login $login: $retorno[message]" . PHP_EOL;
    }
}

