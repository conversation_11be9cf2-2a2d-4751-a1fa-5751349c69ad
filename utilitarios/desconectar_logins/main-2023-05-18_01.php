<?php

/*
	ESTE CÓDIGO FOI UTILIZADO PARA DESCONECTAR OS LOGINS DOS CLIENTES QUE ESTAVAM COM IPV6 DESSINCRONIZADOS ENTRE O IXC E O NOSSO BANCO DE DADOS PGSQL-RADIUS2...
	FOI REALIZADA A LIMPEZA DOS IPV6 DESSES LOGINS ANTES DE DESCONECTÁ-LOS, PARA REALOCAR POSTERIORMENTE, DE FORMA QUE FIQUEM SINCRONIZADOS
*/
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/utils.php';
require_once __DIR__ . '/../../common/Logger.php';

function main()
{
	$dbh_ixc = getConnectionIXC();

	$api = getIxcApi();


	$params = array(
		'qtype' => 'ixc_logs.id',
		'query' => '',
		'oper' => '!=',
		'sortname' => 'ixc_logs.id',
		'sortorder' => 'desc',
		'page' => '1',
		'rp' => '90000',
		'grid_param' => json_encode([
			[
				'TB' => 'ixc_logs.data',
				'OP'=>'>',
				'P'=>'2023-05-17 17:00:00'
			],
			[
				'TB' => 'ixc_logs.data',
				'OP'=>'<',
				'P'=>'2023-05-17 17:12:00'
			]
		]));
	
	$api->get('ixc_logs', $params);
	$retorno = $api->getRespostaConteudo(true);
	$registros = $retorno['registros'];

	$logins = [];
	$logins_ids = [];
	foreach($registros as $registro)  {
		$campos = json_decode($registro['campos'], true);

		if(isset($campos['pd_ipv6']) && $campos['pd_ipv6'] === ''){
			// echo $registro['data'] . PHP_EOL;
			// echo $campos['login'] . PHP_EOL;
			// echo PHP_EOL;

			$logins[] = $campos['login'];
			$logins_ids[] = $registro['id_tabela'];
		}
	}
	
	echo sizeof($logins) . PHP_EOL;
	// var_dump($logins_ids);

	// return;

	$i = 0;
	foreach($logins_ids as $login_id) {
		// if($i++ > 1)
		// 	break;

		$params = array(
			'id' => $login_id
		);
		
		$api->get('desconectar_clientes',$params);
		$retorno = $api->getRespostaConteudo(true);
		if(isset($retorno['message']) && strpos($retorno['message'], 'sucesso') !== false) {
			echo "Login desconectado com sucesso: $login_id" . PHP_EOL;
		}
		else {
			echo "Erro ao desconectar o login de ID $login_id" . PHP_EOL;
		}
	}
}

main();



/*
$params = array(
    'id' => 51803 // ID LOGIN
);

$api->get('desconectar_clientes',$params);
echo $api->getRespostaConteudo(false);
var_dump($retorno);
 */