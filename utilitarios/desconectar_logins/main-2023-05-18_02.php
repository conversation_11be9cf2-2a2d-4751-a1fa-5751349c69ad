<?php

/*
	<PERSON><PERSON><PERSON> CÓDIGO FOI UTILIZADO PARA DESCONECTAR OS LOGINS DOS CLIENTES QUE ESTAVAM COM IPV6 DESSINCRONIZADOS ENTRE O IXC E O NOSSO BANCO DE DADOS PGSQL-RADIUS2...
	FOI REALIZADA A LIMPEZA DOS IPV6 DESSES LOGINS ANTES DE DESCONECTÁ-LOS, PARA REALOCAR POSTERIORMENTE, DE FORMA QUE FIQUEM SINCRONIZADOS
*/
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/utils.php';
require_once __DIR__ . '/../../common/Logger.php';

function main()
{
	$dbh_ixc = getConnectionIXC();

	$api = getIxcApi();


	$logins_ids = [
		71305,16653,51853,51845,19806,71459,71329,50949,71315,50421,27717,36355,31679,29374,37786,18887,23280,16756,21906,17278,24739,28048,33853,35340,36367,36863,24215,30149,29666,35469,41736,33073,48937,50579,51189,51739,35765,52357,41615,55811,43458,51713,57033,57531,58491,58653,50933,49939,60247,51175,35904,45367,59121,61377,61609,61257,62715,57007,62873,62891,60063,63567,63629,64547,65937,66001,63555,56741,68403,68675,63413,60455,63305,33478,66671,34337,56845,62191,70765
	];
	
	echo sizeof($logins_ids) . PHP_EOL;

	$i = 0;
	foreach($logins_ids as $login_id) {
		$params = array(
			'id' => $login_id
		);
		
		$api->get('desconectar_clientes',$params);
		$retorno = $api->getRespostaConteudo(true);
		if(isset($retorno['message']) && strpos($retorno['message'], 'sucesso') !== false) {
			echo "Login desconectado com sucesso: $login_id" . PHP_EOL;
		}
		else {
			echo "Erro ao desconectar o login de ID $login_id" . PHP_EOL;
		}
	}
}

main();



/*
$params = array(
    'id' => 51803 // ID LOGIN
);

$api->get('desconectar_clientes',$params);
echo $api->getRespostaConteudo(false);
var_dump($retorno);
 */