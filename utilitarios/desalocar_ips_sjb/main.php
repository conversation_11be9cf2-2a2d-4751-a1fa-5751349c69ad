<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/Logger.php';

main();

function main() {
	$api = getIxcApi();
	$dbh_ixc = getConnectionIxc();
	$dbh_noc = getConnectionNoc();

	$stmt = $dbh_noc->prepare("SELECT login FROM (SELECT rp.framedipaddress AS range,
	(CASE
	WHEN ru.endereco_padrao_cliente IS NULL OR ru.endereco_padrao_cliente = 'S' OR (ru.endereco_padrao_cliente = 'N' AND (ru.cidade = 0 OR ru.cidade IS NULL)) THEN
	ci1.nome
	ELSE ci2.nome
END) AS cidade_cliente,
ru.login
FROM fdwtables.radippool rp
LEFT JOIN fdwtables.ixc_radusuarios ru ON ru.ip = host(rp.framedipaddress)
LEFT JOIN fdwtables.ixc_cliente cl ON cl.id = ru.id_cliente
LEFT JOIN fdwtables.ixc_cidade ci1 ON ci1.id = cl.cidade
LEFT JOIN fdwtables.ixc_cidade ci2 ON ci2.id = ru.cidade
WHERE rp.pool_name = 'Pool And AdsShopping CPD_1'
AND host(rp.framedipaddress) LIKE  '100%'
AND host(rp.framedipaddress) IN
(SELECT ip FROM fdwtables.ixc_radusuarios WHERE ip != '' AND ip IS NOT NULL)
ORDER BY rp.framedipaddress DESC)
sub WHERE sub.cidade_cliente = 'São João da Boa Vista';");
	$stmt->execute();

	$rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

	$i = 1;
	foreach ($rows as $row) {

		$login = $row['login'];

		echo $i++ . ' - ' . $login . PHP_EOL;

		$radusuario = getRadusuario($login);

		$radusuario['fixar_ip'] = 'N';
		$radusuario['ip'] = '';

		$radusuario['fixar_ipv6'] = 'N';
		$radusuario['framed_fixar_ipv6'] = 'N';
		$radusuario['pd_ipv6'] = '';
		$radusuario['framed_pd_ipv6'] = '';

		updateRadusuario($radusuario);
	}
}