<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/Logger.php';

$dbh_ixc = getConnectionIXC();
$api = getIxcApi();

// array de parâmetros do método
$params = array(
	// 'boletos' => 4188327, // id da fatura
	'boletos' => 4178295,
	'juro' => 'N', // 'S'->SIM e 'N'->NÃO para cálculo de júro
	'multa' => 'N', // 'S'->SIM e 'N'->NÃO para cálculo de multa
	'atualiza_boleto' => 'N', // 'S'->SIM e 'N'->NÃO para atualizar o boleto
	'tipo_boleto' => 'dados'
);

// execução do método GET API
$api->get('get_boleto', $params);
$retorno = $api->getRespostaConteudo(true); // false para retorno em JSON e true para retorno em ARRAY PHP
// printando resultado
var_dump($retorno);

var_dump($retorno[0]['nosso_numero']);