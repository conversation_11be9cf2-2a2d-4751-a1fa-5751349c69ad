<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/Logger.php';

$api = getIxcApi();

$params = array(
    'id' => '82099',
    'id_transmissor' => '422',
    'ponid' => '0-0-1-1',
    'nome' => 'ZTEGcbde3e56',
    'onu_tipo' => '5506-sfu',
    'tipo_autenticacao' => 'MAC',
    'mac' => 'ZTEGcbde3e56',
    'slotno' => '1',
    'ponno' => '1',
    'onu_numero' => '85',
    'ip_gerencia' => '20',
    'login_onu_cliente' => '30',
    'senha_onu_cliente' => '30',
    'porta_telnet_onu_cliente' => '10',
    'endereco_padrao_cliente' => 'S',
    'radpop_estrutura' => 'N',
    'porta_web_onu_cliente' => '0',
    'onu_compartilhada' => 'N',
    'onu_rede_neutra' => 'N',
    'status_potencia' => 'indefinido',
);
$api->delete('radpop_radio_cliente_fibra', '82099');

$retorno = $api->getRespostaConteudo(true);

var_dump($retorno);