<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/Logger.php';

$api = getIxcApi();
$dbh = getConnectionIxc();

$stmt = $dbh->prepare("SELECT ar.id
FROM fn_areceber ar
	INNER JOIN cliente cl ON cl.id = ar.id_cliente
WHERE ar.status = 'A'
	AND ar.data_vencimento BETWEEN '2022-01-01' AND '2022-01-31'
ORDER BY ar.data_vencimento ASC");
$stmt->execute();

$ids = [];

// while($id = $stmt->fetchColumn()) {
// 	$ids[] = $id;
// }

$i = 0;
// foreach($ids as $id) {
	// array de parâmetros do método
	$params = array(
		'qtype' => 'radacct.framedipaddress',
		'query' => '',
		'oper' => '=',
		'sortname' => 'radacct.radacctid',
		'sortorder' => 'desc',
		'page' => '1',
		'rp' => '5000',
		'grid_param' => [
			[
				'TB' => 'radacct.acctstarttime',
				'OP' => '<=',
				'P' => '07/01/2021 22:44:58'
			],
			[
				'TB' => 'radacct.acctstoptime',
				'OP' => '>=',
				'P' => '07/01/2021 22:47:56'
			],
			[
				'TB' => 'radacct.framedipaddress',
				'OP' => '=',
				'P' => '**************'
			]
		]
	);

	// 2021-01-07 22:44:58 - 22:47:56

	// execução do método GET API
	$api->get('radacct', $params);
	$retorno = $api->getRespostaConteudo(true); // false para retorno em JSON e true para retorno em ARRAY PHP

	$registros = $retorno['registros'];

	foreach($registros as $registro) {
		echo "acctstarttime: $registro[acctstarttime]" . PHP_EOL;
		echo "acctstoptime: $registro[acctstoptime]" . PHP_EOL;
	}

	echo sizeof($registros);

	// if (strpos($retorno, 'A carteira') !== false) {
	// 	echo 'Encontrado: ' . $id . PHP_EOL;
	// 	break;
	// }
	// else {
	// 	echo $i++ . PHP_EOL;
	// }
// }