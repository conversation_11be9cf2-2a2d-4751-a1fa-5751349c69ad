<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/utils.php';
require_once __DIR__ . '/../../common/Logger.php';

function main()
{
	$api = getIxcApi();
    
    $params = array(
        'qtype' => 'radusuarios.id',
        'query' => '8007',
        'oper' => '=',
        'sortname' => 'radusuarios.id',
        'sortorder' => 'asc',
        'page' => '1',
        'rp' => '1'
    );
    $api->get('radusuarios', $params);
	$resposta = $api->getRespostaConteudo(true); // false para json | true para array

    $row = $resposta['registros'][0];

    $row['interface_transmissao_fibra'] = 1398;

    echo "login: $row[login]" . PHP_EOL;
    echo "interface_transmissao: $row[interface_transmissao]" . PHP_EOL;
    echo "interface_transmissao_fibra: $row[interface_transmissao_fibra]" . PHP_EOL;

    $api->put('radusuarios', $row, $row['id']);
	$resposta = $api->getRespostaConteudo(true);
}

main();