<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/utils.php';
require_once __DIR__ . '/../../common/Logger.php';

function main()
{
	$dbh_ixc = getConnectionIXC();

	$api = getIxcApi();
    
    $params = array(
        'qtype' => 'cliente_contrato_assinatura_termo.id',
        'query' => '11',
        'oper' => '=',
        'sortname' => 'cliente_contrato_assinatura_termo.id',
        'sortorder' => 'asc',
        'page' => '1',
        'rp' => '1'
    );
    $api->get('cliente_contrato_assinatura_termo', $params);
	$resposta = $api->getRespostaConteudo(true); // false para json | true para array

    $termo = $resposta['registros'][0];

    $termo['assinado'] = 'N';

    $update = updateRow($termo, 'cliente_contrato_assinatura_termo');
    var_dump($update);
}

main();