<?php

require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/utils.php';
require_once __DIR__ . '/../../common/Logger.php';

$api = getIxcApi();

$params = array(
    'id' => '110793'
);
$api->get('cliente_contrato_imprimir_contrato_17678', $params);
$retorno = $api->getRespostaConteudo(false); // false para json | true para array
// var_dump($retorno);


$base64String = $retorno;

// Decodifique a string base64
$pdfData = base64_decode($base64String);


// Cria um nome de arquivo temporário
$filename = tempnam(sys_get_temp_dir(), 'pdf');

// Escreve os dados binários no arquivo temporário
file_put_contents($filename, $pdfData);


// URL do endpoint para o qual você está enviando o arquivo
$endpointURL = "https://central.telemidia.net.br/webservice/v1/cliente_arquivos";

// Criar um array associativo com os dados do formulário
$formData = [
    'descricao' => 'Teeeste22',
    'local_arquivo' => new CURLFile($filename, 'application/pdf', 'contrato.pdf'),
    'id_cliente' => '73', // Nome do arquivo no lado do servidor
];

// Codificar as credenciais para o cabeçalho de autorização Basic
$authorizationHeader = 'Authorization: Basic Mjo1MzYzNDk5N2MzNmMyM2Q0NTExNThkNjk1Njg1YjJhODIxYjIwZTg3NDhjODY4ZmE2YzdhMjlmMGZjZjRkNjMy';

// Inicializar o objeto cURL
$ch = curl_init();

// Configurar as opções cURL
curl_setopt($ch, CURLOPT_URL, $endpointURL);
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, $formData);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

// Adicionar o cabeçalho de autorização Basic
curl_setopt($ch, CURLOPT_HTTPHEADER, [$authorizationHeader]);

echo 'aaaaaaaaaa';

// Executar a requisição cURL
$response = curl_exec($ch);

echo 'bbbbbbbbbb';

// Verificar se ocorreu algum erro
if (curl_errno($ch)) {
    echo 'Erro ao enviar a requisição: ' . curl_error($ch);
} else {
    // Processar a resposta do servidor
    echo 'Resposta do servidor: ' . $response;
}

// Fechar a sessão cURL
curl_close($ch);