<?php

require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/utils.php';
require_once __DIR__ . '/../../common/Logger.php';

$api = getIxcApi();

$params = array(
    'id' => '110793'
);
$api->get('cliente_contrato_imprimir_contrato_17678', $params);
$retorno = $api->getRespostaConteudo(false); // false para json | true para array
var_dump($retorno);


$base64String = $retorno;

// Decodifique a string base64
$pdfData = base64_decode($base64String);

// Crie um nome de arquivo único para o PDF
$filename = 'documento_' . time() . '.pdf';

// Caminho onde o arquivo será salvo
$filepath = __DIR__ . '/' . $filename;

// Salve o conteúdo decodificado como um arquivo PDF
file_put_contents($filepath, $pdfData);