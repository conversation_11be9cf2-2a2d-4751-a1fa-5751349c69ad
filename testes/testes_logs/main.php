<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/Logger.php';

$api = getIxcApi();
$params = array(
	'qtype' => 'ixc_logs.id_tabela',
	'query' => '95623',
	// 'query' => '85377',
	'oper' => '=',
	'sortname' => 'ixc_logs.data',
	'sortorder' => 'desc',
	'page' => '1',
	'rp' => '10',
	'grid_param' => json_encode([
		[
			'TB' => 'ixc_logs.tabela',
			'OP' => '=',
			'P' => 'cliente_contrato'
		]
	])
);

$api->get('ixc_logs', $params);
$retorno = $api->getRespostaConteudo(true);

$registros = $retorno['registros'];

foreach($registros as $registro) {
	var_dump($registro);
}