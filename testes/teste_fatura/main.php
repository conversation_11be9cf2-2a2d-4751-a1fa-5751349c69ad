<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/Logger.php';

$api = getIxcApi();
$dbh = getConnectionIxc();

$stmt = $dbh->prepare("SELECT ar.id
FROM fn_areceber ar
	INNER JOIN cliente cl ON cl.id = ar.id_cliente
WHERE ar.status = 'A'
	AND ar.data_vencimento BETWEEN '2022-01-01' AND '2022-01-31'
ORDER BY ar.data_vencimento ASC");
$stmt->execute();

$ids = [];

// while($id = $stmt->fetchColumn()) {
// 	$ids[] = $id;
// }

$i = 0;
// foreach($ids as $id) {
	// array de parâmetros do método
	$params = array(
		'boletos' => 2676207, // id da fatura
		'juro' => 'N', // 'S'->SIM e 'N'->NÃO para cálculo de júro
		'multa' => 'N', // 'S'->SIM e 'N'->NÃO para cálculo de multa
		'atualiza_boleto' => 'N', // 'S'->SIM e 'N'->NÃO para atualizar o boleto
		'tipo_boleto' => 'arquivo', // tipo de método que será executado
		'base64' => 'S' // para retornar arquivo em formato base64 informe 'S', para retornar binário, informe 'N' ou não informe
	);

	// execução do método GET API
	$api->get('get_boleto', $params);
	$retorno = $api->getRespostaConteudo(false); // false para retorno em JSON e true para retorno em ARRAY PHP

	echo $retorno;
	return;

	// if (strpos($retorno, 'A carteira') !== false) {
	// 	echo 'Encontrado: ' . $id . PHP_EOL;
	// 	break;
	// }
	// else {
	// 	echo $i++ . PHP_EOL;
	// }
// }