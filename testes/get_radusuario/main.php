<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/utils.php';
require_once __DIR__ . '/../../common/Logger.php';

function main()
{
	$api = getIxcApi();
    $params = array(
        'qtype' => 'radusuarios.id',
        'query' => '81809',
        'oper' => '=',
        'page' => '1',
        'rp' => '20',
        'sortname' => 'radusuarios.id',
        'sortorder' => 'desc'
    );
    $api->get('radusuarios', $params);    
	$resposta = $api->getRespostaConteudo(true);
    $radusuario = $resposta['registros'][0];

    // Se não adicionar essa linha abaixo, o "interface_transmissao" fica "0" após o PUT
    $radusuario['interface_transmissao_fibra'] = $radusuario['interface_transmissao'];

    $api->put('radusuarios', $radusuario, $radusuario['id']);
	$resposta = $api->getRespostaConteudo(true);
}

main();