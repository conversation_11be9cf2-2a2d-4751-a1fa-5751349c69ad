<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/Logger.php';

// CLIENTE TESTE
// id_cliente: 73
// id_contrato: 76007
// id_fatura_a_renegociar: 3633171

// POST 1. Renegociar selecionados
// renegociarSelecionados();
function renegociarSelecionados()
{
    $api = getIxcApi();

    $params = [
        'get_id' => '2593105',
    ];
    $api->post('renegociar_selecionados', $params);

    $retorno = $api->getRespostaConteudo(true);

    var_dump($retorno);
}

// PUT 2. Renegociação Wizard (preencher dados obrigatórios)
// renegociacaoWizard(32075);
function renegociacaoWizard($id_renegociacao)
{
    $api = getIxcApi();

    $params = [
        "id_filial" => "2", //Obrigatório? (GET)
        "id_conta" => "174", //Obrigatório (GET)
        "id_cliente" => "73", //Obrigatório (GET)
        "data_emissao" => "18/02/2025", //Obrigatório (GET)
        "previsao" => "S", //Obrigatório (GET)
        "id_carteira_cobranca" => "8", //Obrigatório
        "id_condicao_pagamento" => "1", //Obrigatório
        // "vendedor_renegociacao" => "",
        "contrato_renegociacao" => "76007", //Obrigatório (GET)
        "data_vencimento" => "30/03/2025",
        "valor_parcelas" => "57.12", //Obrigatório? (GET)
        "valor_acrescimos" => "0.00",
        "valor_descontos" => "0.00",
        "valor_total" => "57.12", //Obrigatório? (GET)
        "valor_renegociado" => "57.12", //Obrigatório
        "acre_juros_multa" => "",
        "valor_total_pagar" => "57.12", //Obrigatório
        "status" => "A", //Obrigatório
        "data_finalizada" => "",
        "finalizar" => "N" //Obrigatório
    ];
    $api->put('fn_renegociacao_wiz', $params, $id_renegociacao);

    $retorno = $api->getRespostaConteudo(true);

    var_dump($retorno);
}

// POST 4. Calcula multa/juros dos títulos da renegociação
// calcularMultaJuros(32075);
calcularMultaJuros(32715);
function calcularMultaJuros($id_renegociacao)
{
    $api = getIxcApi();

    $params = [
        'id' => $id_renegociacao,
        'id_carteira_cobranca' => '8',
        'id_condicao_pagamento' => '2'
    ];
    $api->post('calcula_juros_multa', $params);

    $retorno = $api->getRespostaConteudo(true);

    var_dump($retorno);
}

//PUT 5. Finalizar Renegociação
// finalizarRenegociacao(32075);
function finalizarRenegociacao($id_renegociacao)
{
    $api = getIxcApi();

    $params = [
        "id_filial" => "2", //Obrigatório? (GET)
        "id_conta" => "174", //Obrigatório (GET)
        "id_cliente" => "73", //Obrigatório (GET)
        "data_emissao" => "18/02/2025", //Obrigatório (GET)
        "previsao" => "S", //Obrigatório (GET)
        "id_carteira_cobranca" => "8", //Obrigatório
        "id_condicao_pagamento" => "1", //Obrigatório
        // "vendedor_renegociacao" => "",
        "contrato_renegociacao" => "76007", //Obrigatório (GET)
        "data_vencimento" => "30/03/2025",
        "valor_parcelas" => "57.12", //Obrigatório? (GET)
        "valor_acrescimos" => "0.00",
        "valor_descontos" => "0.00",
        "valor_total" => "57.12", //Obrigatório? (GET)
        "valor_renegociado" => "57.12", //Obrigatório
        "acre_juros_multa" => "",
        "valor_total_pagar" => "57.12", //Obrigatório
        "status" => "A", //Obrigatório
        "data_finalizada" => "",
        "finalizar" => "S" //Obrigatório
    ];
    $api->put('fn_renegociacao_wiz', $params, $id_renegociacao);

    $retorno = $api->getRespostaConteudo(true);

    var_dump($retorno);
}

function getRenegociacao($id_renegociacao)
{
    $api = getIxcApi();

    $params = [
        'qtype' => 'id',
        'query' => $id_renegociacao,
        'oper' => '=',
        'sortname' => 'id',
        'sortorder' => 'asc',
        'page' => '1',
        'rp' => '3',
    ];

    $api->get('fn_renegociacao_wiz', $params);

    $retorno = $api->getRespostaConteudo(true);

    $renegociacao = null;
    if ($retorno && isset($retorno['registros']) && $retorno['registros'] > 0) {
        $renegociacao = $retorno['registros'][0];

        foreach ($renegociacao as $chave => $valor) {
            if (preg_match('/^\d{4}-\d{2}-\d{2}$/', $valor)) {
                if (strpos($valor, '0000-') === 0) {
                    $renegociacao[$chave] = '';
                }
                else {
                    $renegociacao[$chave] = date('d/m/Y', strtotime($valor));
                }
            }
        }
    }

    return $renegociacao;
}
