<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/utils.php';
require_once __DIR__ . '/../../common/Logger.php';

main();

function main()
{
    $api = getIxcApi();

    $params = array(
        'qtype' => 'radacct.framedipaddress',
        'query' => '**************',
        'oper' => '=',
        'sortname' => 'radacct.radacctid',
        'sortorder' => 'asc',
        'page' => '1',
        'rp' => '90000'
    );
    $api->get('radacct', $params);
    $retorno = $api->getRespostaConteudo(true); // false para json | true para array

    file_put_contents('output.json', json_encode($retorno, JSON_PRETTY_PRINT));
}
