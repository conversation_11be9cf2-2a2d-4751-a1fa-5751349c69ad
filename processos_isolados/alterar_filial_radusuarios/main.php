<?php
require_once __DIR__ . '/../../common/config.php';
$successLogFile = 'execution_2025-05-09.log';
$errorLogFile = 'execution_errors_2025-05-09.log';

main();

function main() {
    global $successLogFile;
    global $errorLogFile;
	$dbh = getConnectionIxc();

	$stmt = $dbh->prepare("SELECT ru.*
		FROM radusuarios ru
        INNER JOIN cliente_contrato cc ON cc.id = ru.id_contrato
		WHERE ru.id_filial != 2
            AND cc.status NOT IN ('D', 'I');");
	$stmt->execute();

	$usuarios = $stmt->fetchAll(PDO::FETCH_ASSOC);

	foreach ($usuarios as $index => $usuario) {
        $usuario['id_filial'] = 2;
        $resposta = updateRadusuario($usuario);
        
        if (isset($resposta['type']) && $resposta['type'] === 'success') {
            $mensagem = date('Y-m-d H:i:s') . " - Filial do login {$usuario['login']} alterada para 2\n";
            $logFile = $successLogFile;
        }
        else {
            $mensagem = date('Y-m-d H:i:s') . " - Erro ao atualizar filial do login {$usuario['login']}: {$resposta['message']}\n";
            $logFile = $errorLogFile;
        }

        file_put_contents($logFile, $mensagem, FILE_APPEND);
        
        echo "\r" . str_repeat(" ", 50) . "\r" . ($index + 1) . "/" . count($usuarios) . " - (" . number_format(($index + 1) / count($usuarios) * 100, 2) . "%) - Alterando filial do login {$usuario['login']}";
        // break;
    }
}
