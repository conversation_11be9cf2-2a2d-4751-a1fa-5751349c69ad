<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/Logger.php';

$f = fopen(__DIR__ . '/lock', 'w') or die('Cannot create lock file');
if (flock($f, LOCK_EX | LOCK_NB)) {
	main();
}

function main() {
	$dbh_noc = getConnectionNoc();
	$dbh_ixc = getConnectionIxc();

	$sql = "SELECT login, pd_ipv6, framed_pd_ipv6, ra_pd.username AS ra_pd_username, ra_pd.allocated_prefix AS ra_pd, ra_fpd.username AS ra_fpd_username, ra_fpd.allocated_prefix AS ra_fpd
	FROM fdwtables.ixc_radusuarios ru
	LEFT JOIN fdwtables.radipv6_allocated ra_pd ON ra_pd.allocated_prefix::text = ru.pd_ipv6
		AND ra_pd.attribute = 'Delegated-Ipv6-Prefix'
	LEFT JOIN fdwtables.radipv6_allocated ra_fpd ON ra_fpd.allocated_prefix::text = ru.framed_pd_ipv6
		AND ra_fpd.attribute = 'Framed-IPv6-Prefix'
	WHERE (
		(ru.pd_ipv6 IS NOT NULL AND ru.pd_ipv6 != '')
			OR
		(ru.framed_pd_ipv6 IS NOT NULL AND ru.framed_pd_ipv6 != '')
		)
		AND
		(
			ru.login NOT IN (
				SELECT username FROM fdwtables.radipv6_reserved
			)
			OR
			ru.pd_ipv6 NOT IN (SELECT allocated_prefix::text FROM fdwtables.radipv6_allocated)
			OR
			ru.framed_pd_ipv6 NOT IN (SELECT allocated_prefix::text FROM fdwtables.radipv6_allocated)
		);";
	$stmt = $dbh_noc->prepare($sql);
	$stmt->execute();

	$loginsToClean = $stmt->fetchAll(PDO::FETCH_ASSOC);

	// echo sizeof($loginsToClean);
	// exit;
	
	$i = 0;
	foreach($loginsToClean as $login) {
		// if ($i++ == 2)
		// 	break;

		$stmt = $dbh_ixc->prepare("SELECT * FROM radusuarios ru WHERE ru.login = ?;");
		$stmt->execute(array($login['login']));

		$radusuario = $stmt->fetch(PDO::FETCH_ASSOC);

		$radusuario['pd_ipv6'] = '';
		$radusuario['framed_pd_ipv6'] = '';

		$radusuario['fixar_ipv6'] = 'H';
		$radusuario['framed_fixar_ipv6'] = 'H';

		var_dump(updateRadusuario($radusuario));
	}
}