<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/utils.php';
require_once __DIR__ . '/../../common/Logger.php';

$dbh_ixc = getConnectionIXC();
$api = getIxcApi();

// ------------------------------------------------
// Parte 1 - GERAR ATENDIMENTOS

$contratos = file('contratos.txt');
foreach ($contratos as $contrato) {
    $id_contrato = trim($contrato);
    $id_cliente = getIdClienteByIdContrato($id_contrato);

    $api->post('su_ticket', [
        'tipo' => 'C',
        'id_cliente' => $id_cliente,
        'id_contrato' => $id_contrato,
        'id_assunto' => 4501,
        'prioridade' => 'M',
        'id_ticket_origem' => 'I',
        'interacao_pendente' => 'N',
        'titulo' => 'COM-MULTA FIDELIDADE NAO GERADA - DEBITO ANTERIOR A 15/08/2021', // Descrição assunto
        'id_ticket_setor' => 5, // Departamento
        'menssagem' => 'CLIENTE COM DEBITO A RECEBER. NAO SERÁ FEITA A CONFERÊNCIA PARA GERAÇÃO EXTEMPORÂNEA DE MULTA DE FIDELIDADE. CONTRATO CANCELADO PELA ROTINA AUTOMÁTICA EM 14/02/2023.', // Descrição
        'status' => 'T',
        'su_status' => 'S' // Status
    ]);
    
    $retorno = $api->getRespostaConteudo(true);
    
    if ($retorno['type'] === 'success') {
        echo "Gerado atendimento para o contrato $id_contrato" . PHP_EOL;
    } else {
        echo "Erro ao gerar atendimento para o contrato $id_contrato: $retorno[message]" . PHP_EOL;
    }
}

// ------------------------------------------------

function getClienteAndContratos($id_cliente) {
    $dbh_ixc = getConnectionIxc();

	$sql = "SELECT cl.*,
        (SELECT count(*) FROM cliente_contrato cc
            WHERE id_cliente = cl.id AND cc.status NOT IN ('D', 'I')) AS total_contratos_ativos
    FROM cliente cl
    WHERE cl.id = ?
    LIMIT 1;";
    
	$stmt = $dbh_ixc->prepare($sql);
	$query = $stmt->execute(array($id_cliente));
	$cliente = $stmt->fetch(PDO::FETCH_ASSOC);

	return $cliente;
}