<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/Logger.php';

$dbh_ixc = getConnectionIXC();
$api = getIxcApi();

// ------------------------------------------------
// Parte 2 - INATIVAR CLIENTES SEM CONTRATOS ATIVOS

$contratos = file('contratos.txt');
foreach ($contratos as $contrato) {
    $id_contrato = trim($contrato);
    $id_cliente = getIdClienteByIdContrato($id_contrato);
    $cliente = getClienteAndContratos($id_cliente);

    if ($cliente['total_contratos_ativos'] == 0) {
        $cliente['ativo'] = 'N';

        $retorno = updateCliente($cliente);

        if ($retorno['type'] === 'success') {
            echo "Inativado cliente $id_cliente" . PHP_EOL;
        } else {
            echo "Erro ao inativar cliente $id_cliente: $retorno[message]" . PHP_EOL;
        }
    }
}

// ------------------------------------------------

function getClienteAndContratos($id_cliente) {
    $dbh_ixc = getConnectionIxc();

	$sql = "SELECT cl.*,
        (SELECT count(*) FROM cliente_contrato cc
            WHERE id_cliente = cl.id AND cc.status NOT IN ('D', 'I')) AS total_contratos_ativos
    FROM cliente cl
    WHERE cl.id = ?
    LIMIT 1;";
    
	$stmt = $dbh_ixc->prepare($sql);
	$query = $stmt->execute(array($id_cliente));
	$cliente = $stmt->fetch(PDO::FETCH_ASSOC);

	return $cliente;
}

function getIdClienteByIdContrato($id_contrato) {
    $dbh_ixc = getConnectionIxc();

	$sql = "SELECT id_cliente
        FROM cliente_contrato cc
        WHERE cc.id = ?
        LIMIT 1;";

	$stmt = $dbh_ixc->prepare($sql);
	$query = $stmt->execute(array($id_contrato));
	$id_cliente = $stmt->fetch(PDO::FETCH_COLUMN);

	return $id_cliente;
}