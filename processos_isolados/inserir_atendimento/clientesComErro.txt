33297: Cliente contribuinte n�o pode ter IE/RG = ISENTO, altere o par�metro Contribuinte ICMS ou o conte�do do campo IE/RG.
33297: Cliente contribuinte n�o pode ter IE/RG = ISENTO, altere o par�metro Contribuinte ICMS ou o conte�do do campo IE/RG.
27017: Cliente contribuinte n�o pode ter IE/RG = ISENTO, altere o par�metro Contribuinte ICMS ou o conte�do do campo IE/RG.
42688: Cliente contribuinte n�o pode ter IE/RG = ISENTO, altere o par�metro Contribuinte ICMS ou o conte�do do campo IE/RG.
32338: Cliente contribuinte n�o pode ter IE/RG = ISENTO, altere o par�metro Contribuinte ICMS ou o conte�do do campo IE/RG.
49199: A Raz�o Social/Nome n�o pode conter dois espa�os em sequ�ncia, remova-os!
40605: Cliente contribuinte n�o pode ter IE/RG = ISENTO, altere o par�metro Contribuinte ICMS ou o conte�do do campo IE/RG.
28429: Cliente contribuinte n�o pode ter IE/RG = ISENTO, altere o par�metro Contribuinte ICMS ou o conte�do do campo IE/RG.
13192: Cliente contribuinte n�o pode ter IE/RG = ISENTO, altere o par�metro Contribuinte ICMS ou o conte�do do campo IE/RG.
13192: Cliente contribuinte n�o pode ter IE/RG = ISENTO, altere o par�metro Contribuinte ICMS ou o conte�do do campo IE/RG.
44682: Cliente contribuinte n�o pode ter IE/RG = ISENTO, altere o par�metro Contribuinte ICMS ou o conte�do do campo IE/RG.
34003: Cliente contribuinte n�o pode ter IE/RG = ISENTO, altere o par�metro Contribuinte ICMS ou o conte�do do campo IE/RG.
36938: Cliente contribuinte n�o pode ter IE/RG = ISENTO, altere o par�metro Contribuinte ICMS ou o conte�do do campo IE/RG.
38069: Cliente contribuinte n�o pode ter IE/RG = ISENTO, altere o par�metro Contribuinte ICMS ou o conte�do do campo IE/RG.
41891: Cliente contribuinte n�o pode ter IE/RG = ISENTO, altere o par�metro Contribuinte ICMS ou o conte�do do campo IE/RG.
42053: Cliente contribuinte n�o pode ter IE/RG = ISENTO, altere o par�metro Contribuinte ICMS ou o conte�do do campo IE/RG.
40914: Cliente contribuinte n�o pode ter IE/RG = ISENTO, altere o par�metro Contribuinte ICMS ou o conte�do do campo IE/RG.
39443: Cliente contribuinte n�o pode ter IE/RG = ISENTO, altere o par�metro Contribuinte ICMS ou o conte�do do campo IE/RG.
32568: Cliente contribuinte n�o pode ter IE/RG = ISENTO, altere o par�metro Contribuinte ICMS ou o conte�do do campo IE/RG.
36764: Cliente contribuinte n�o pode ter IE/RG = ISENTO, altere o par�metro Contribuinte ICMS ou o conte�do do campo IE/RG.
34279: Cliente contribuinte n�o pode ter IE/RG = ISENTO, altere o par�metro Contribuinte ICMS ou o conte�do do campo IE/RG.
25180: Cliente contribuinte n�o pode ter IE/RG = ISENTO, altere o par�metro Contribuinte ICMS ou o conte�do do campo IE/RG.
13117: Preencha um CPF/CNPJ v�lido.
37335: Cliente contribuinte n�o pode ter IE/RG = ISENTO, altere o par�metro Contribuinte ICMS ou o conte�do do campo IE/RG.
26867: Cliente contribuinte n�o pode ter IE/RG = ISENTO, altere o par�metro Contribuinte ICMS ou o conte�do do campo IE/RG.
33957: Cliente contribuinte n�o pode ter IE/RG = ISENTO, altere o par�metro Contribuinte ICMS ou o conte�do do campo IE/RG.
29324: Cliente contribuinte n�o pode ter IE/RG = ISENTO, altere o par�metro Contribuinte ICMS ou o conte�do do campo IE/RG.
32720: Cliente contribuinte n�o pode ter IE/RG = ISENTO, altere o par�metro Contribuinte ICMS ou o conte�do do campo IE/RG.
31182: Cliente contribuinte n�o pode ter IE/RG = ISENTO, altere o par�metro Contribuinte ICMS ou o conte�do do campo IE/RG.
27725: Cliente contribuinte n�o pode ter IE/RG = ISENTO, altere o par�metro Contribuinte ICMS ou o conte�do do campo IE/RG.
30031: Cliente contribuinte n�o pode ter IE/RG = ISENTO, altere o par�metro Contribuinte ICMS ou o conte�do do campo IE/RG.
32438: Cliente contribuinte n�o pode ter IE/RG = ISENTO, altere o par�metro Contribuinte ICMS ou o conte�do do campo IE/RG.
28611: Cliente contribuinte n�o pode ter IE/RG = ISENTO, altere o par�metro Contribuinte ICMS ou o conte�do do campo IE/RG.
27107: Cliente contribuinte n�o pode ter IE/RG = ISENTO, altere o par�metro Contribuinte ICMS ou o conte�do do campo IE/RG.
21672: Cliente contribuinte n�o pode ter IE/RG = ISENTO, altere o par�metro Contribuinte ICMS ou o conte�do do campo IE/RG.
26588: Cliente contribuinte n�o pode ter IE/RG = ISENTO, altere o par�metro Contribuinte ICMS ou o conte�do do campo IE/RG.
27593: Cliente contribuinte n�o pode ter IE/RG = ISENTO, altere o par�metro Contribuinte ICMS ou o conte�do do campo IE/RG.
23582: Cliente contribuinte n�o pode ter IE/RG = ISENTO, altere o par�metro Contribuinte ICMS ou o conte�do do campo IE/RG.
21483: Cliente contribuinte n�o pode ter IE/RG = ISENTO, altere o par�metro Contribuinte ICMS ou o conte�do do campo IE/RG.
11716: Preencha um CPF/CNPJ v�lido.
15197: Cliente contribuinte n�o pode ter IE/RG = ISENTO, altere o par�metro Contribuinte ICMS ou o conte�do do campo IE/RG.
7906: Preencha um CPF/CNPJ v�lido.
11429: O campo 'IE/RG' � de preenchimento obrigat�rio quando o campo 'Contribuinte ICMS' est� marcado como 'Sim'.
18151: O campo 'IE/RG' � de preenchimento obrigat�rio quando o campo 'Contribuinte ICMS' est� marcado como 'Sim'.
17774: O campo 'IE/RG' � de preenchimento obrigat�rio quando o campo 'Contribuinte ICMS' est� marcado como 'Sim'.
4394: Telefone do destinat�rio inv�lido!
11394: Preencha um CPF/CNPJ v�lido.
17947: O campo 'IE/RG' � de preenchimento obrigat�rio quando o campo 'Contribuinte ICMS' est� marcado como 'Sim'.
7429: O campo 'IE/RG' � de preenchimento obrigat�rio quando o campo 'Contribuinte ICMS' est� marcado como 'Sim'.