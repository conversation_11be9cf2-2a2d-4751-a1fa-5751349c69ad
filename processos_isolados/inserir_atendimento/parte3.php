<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/Logger.php';

$dbh_ixc = getConnectionIXC();
$api = getIxcApi();

// ------------------------------------------------
// Parte 3 - LISTAR CLIENTES INATIVADOS E CLIENTES COM ERRO AO INATIVAR

$clientesInativados = $clientesComErro = [];

$log = file('inativarClientes.txt');
foreach ($log as $linha) {
    $linha = trim($linha);

    if (stringContains($linha, 'Inativado cliente'))
        $clientesInativados[] = preg_replace('/[^0-9.]+/', '', $linha);
    else {
        $linha = str_replace('Erro ao inativar cliente ', '', $linha);
        $clientesComErro[] = $linha;
    }
}

foreach ($clientesComErro as $cliente) {
    file_put_contents('clientesComErro.txt', $cliente . PHP_EOL, FILE_APPEND);
}

foreach ($clientesInativados as $cliente) {
    file_put_contents('clientesInativados.txt', $cliente . PHP_EOL, FILE_APPEND);
}

function stringContains($haystack, $needle) {
    return strpos($haystack, $needle) !== false;
}