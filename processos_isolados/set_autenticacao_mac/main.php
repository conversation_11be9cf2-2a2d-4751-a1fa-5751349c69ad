<?php
require_once __DIR__ . '/../../common/config.php';

main();

function main() {
	$dbh = getConnectionIxc();

	$stmt = $dbh->prepare("SELECT ru.*
		FROM radusuarios ru 
		INNER JOIN radpop_radio rpr ON rpr.id = ru.id_transmissor
			AND rpr.descricao LIKE 'AP %'
		WHERE ru.autenticacao_por_mac != 'S' AND ativo = 'S';");
	$stmt->execute();

	$usuarios = $stmt->fetchAll(PDO::FETCH_ASSOC);

	foreach($usuarios as $usuario) {
		$usuario['autenticacao_por_mac'] = 'S';
		$resposta = updateRadusuario($usuario);
	}
}
