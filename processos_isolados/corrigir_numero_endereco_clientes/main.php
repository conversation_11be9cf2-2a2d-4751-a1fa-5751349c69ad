<?php

require_once __DIR__ . '/../../common/config.php';

function main()
{
	$dbh_ixc = getConnectionIxc();

	$stmt = $dbh_ixc->prepare("SELECT *
	FROM cliente
	WHERE numero NOT REGEXP '^[-+]?[0-9]*\.?[0-9]+([eE][-+]?[0-9]+)?$'
		AND UPPER(numero) LIKE '%S/N%'
		AND ativo = 'S';");
	$stmt->execute();

	$i = $stmt->rowCount();
	$j = 0;
	while($cliente = $stmt->fetch(PDO::FETCH_ASSOC)) {
		// Controle de limite de repetições, para testes
		// if($j++ == 1)
		// 	break;

		$cliente['numero'] = 'SN';

		$resposta = updateRow($cliente, 'cliente');

		var_dump($resposta);

		echo --$i . ' - ' . $cliente['id'] . PHP_EOL;
	}
}

main();