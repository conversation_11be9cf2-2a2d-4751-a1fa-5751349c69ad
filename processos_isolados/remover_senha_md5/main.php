<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/utils.php';
require_once __DIR__ . '/../../common/Logger.php';

function main()
{
	$dbh_ixc = getConnectionIXC();

	// Lista LOGs de exclusão de logins (no log consta apenas o ID do login excluido, nao o login)
	$stmt = $dbh_ixc->prepare("SELECT * FROM cliente WHERE ativo = ? AND senha_hotsite_md5 = ?;");

	$stmt->execute(['S', 'S']);
	$clientes = $stmt->fetchAll(PDO::FETCH_ASSOC);

	foreach($clientes as $cliente) {
		echo "Atualizando cliente ID $cliente[id]" . PHP_EOL;
		$cliente['senha_hotsite_md5'] = 'N';
		$resposta = updateCliente($cliente);
		var_dump($resposta);
	}
	echo PHP_EOL;
}

main();