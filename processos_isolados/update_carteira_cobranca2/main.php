<?php
require_once __DIR__ . '/../../common/config.php';

$ids_contratos = "'844','1108','1202','1430','1455','1841','1987','2281','3354','4340','4509','4621','4791','4876','5071','5299','5553','5825','5849','5874','6133','6611','7517','7633','7954','8155','8338','8804','9011','9514','9615','9673','10159','10306','10333','10642','10786','10827','10969','11023','11399','11475','11714','12054','12213','12393','12588','12921','13030','13195','14032','14135','14161','14319','14432','14495','14724','15165','15321','15729','16455','16781','16796','16849','17038','17311','17445','17482','17661','17954','18151','18273','18394','18521','19185','19331','19711','20007','20397','20426','20600','20787','20865','21151','21354','21609','21683','21781','21803','22456','22501','22699','23728','23752','24161','24473','24723','24726','25329','25340','25435','25501','25635','25661','25666','25686','25863','26115','26140','26226','26347','26443','26464','26486','26496','26559','26568','26688','26733','26804','26890','26899','26983','26985','27003','27069','27079','27191','27252','27255','27275','27489','27581','27720','27750','27798','28051','28158','28167','28195','28262','28266','28305','28537','28663','29002','29045','29323','29835','29987','30039','30101','30198','30252','30271','30405','30458','30509','30599','30910','30917','31110','31145','31189','31217','31257','31436','31472','31536','31543','31605','31851','32013','32274','33183','33199','33227','33323','33418','33572','33789','33839','33856','34009','34068','34138','34196','34297','34410','34435','34653','34698','34923','35031','35155','35368','35686','35705','35846','35901','35946','35969','36012','36019','36055','36063','36121','36127','36334','36375','36419','36420','36492','36567','36656','36862','36927','36992','37080','37202','37256','37403','37864','37887','37952','38105','38243','38249','38357','38363','38622','38671','38727','38732','38893','39009','39051','39131','39383','39421','39426','39703','39736','39778','39909','39986','40116','40204','40330','40387','40665','40688','40797','40933','41131','41219','41267','41398','41549','41586','41799','41949','42020','42394','42515','42578','42585','42674','42989','43029','43066','43270','43326','43337','43388','43441','43491','43690','43752','43763','43822','43846','43873','43901','44187','44226','44250','44447','44456','44699','44867','44969','45086','45122','45222','45378','45405','45433','46233','46589','46622','46844','46862','46938','47634','47993','48808','48875','49170','49174','49229','49239','49275','49369','49408','49460','49591','49718','49742','49905','49934','50059','50065','50067','50154','50385','50390','50504','50558','50682','50733','50754','50844','50944','50994','51123','51152','51157','51185','51216','51312','51332','51356','51375','51381','51474','51477','51578','51584','51615','51884','51939','51942','51946','52031','52050','52055','52205','52283','52390','52517','52786','52904','53017','53061','53172','53200','53346','53381','53649','53653','53751','53863','53906','54122','54221','54241','54261','54281','54326','54339','54387','54426','54446','54557','54598','54649','54743','54832','54884','54993','55020','55024','55075','55175','55208','55215','55251','55275','55465','55473','55521','55593','55611','55841','55868','55870','55904','55926','55972','56027','56194','56263','56453','56512','56537','56589','56743','56772','56806','56948','57091','57139','57435','57610','57667','57764','57845','58146','58166','58249','58305','58317','58341','58476','58620','58831','58945','59099','59145','59190','59196','59231','59306','59536','59646','59721','59818','59845','59868','59894','60193','60201','60215','60220','60233','60280','60287','60574','60612','60878','61009','61012','61052','61097','61142','61161','61258','61281','61287','61334','61335','61365','61379','61400','61478','61482','61486','61499','61573','61605','61750','61753','61759','61850','61910','62085','62118','62203','62389','62412','62422','62541','62548','62561','62694','62752','62768','62799','62947','63020','63052','63226','63251','63264','63308','63363','63546','63563','63595','63607','63641','63725','63768','64039','64080','64100','64175','64207','64266','64310','64379','64400','64423','64489','64504','64601','64607','64623','64727','64760','64768','64771','64782','64825','64842','64992','65061','65093','65114','65303','65458','65523','65530','65584','65692','65880','65954','66034','66173','66194','66199','66459','66586','66830','66880','67221','67265','67435','67546','67575','67647','67734','67763','67791','67856','67909','68005','68006','68007','68054','68169','68223','68241','68287','68418','68528','68667','68882','68976','69027','69049','69077','69218','69343','69373','69421','69442','69449','69543','69578','69579','69678','69681','69855','69873','69894','69924','70062','70091','70268','70275','70326','70348','70451','70512','70590','71149','71230','71691','71715','71757','72018','72048','72072','72211','72241','72292','72621','72715','73081','73290','73539','73669','73675','74134','74167','74769','74795','74806','74822','74951','75035','75125','75183','75251','75377','75653','76031','76409','76457','76463','76467','76781','76877','76883','76919','76987','77039','77603','77675','78111','78571','78573','78811','78909','79039','79133','79315','79407','79499','79715','80133','80164','80687','80815','80867','81065','81385','81509','81689','81807','81941','81979','82503','82677','82701','82769','82775','82941','82967','83055','83405','83497','83527','83577','83785','83933','84043','84091','84151','84153','84203','84225','84281','84333','84421','84593','84601','84659','84679','84909','84971','85133','85237','85347','85355','85419','85465','85757','85925','86131','86545','86831','87263','87337','87385','87395','87583','88517','88581','88801','88891','89093','89095','89183','89443','89507','89529','89727','90023','90195','90359','90485','90665','90951','91007','91083','91145','91407','91559','91645','91831','91999','92133','92333','92403','92463','92597','93421','93611','93681','93809','93953','94071','94329','94443','94495','94843','94871','94881','94885','94903','94909','94911','94913','94927','94929','94953','94961','94973','95017','95049','95089','95093','95095','95109','95173','95177','95185','95215','95235','95267','95371','95391','95459','95473','95647','95765','95951','96011','96251','96277','96609','96741','97005','97029','97337','97575','97585','97633','97697','97787','97877','98247','98809','98957','99447','99583','99641','99877','99887','100373','100489','100501','101429','101789','102077','102287','102411','102573','102819','103259','103419','103491','103513','103613','104293','104383','104605','104629','104705','104823','104999','105181','105259','105309','105313','105451','105607','106129','106179','106249','106333','106943','107087','107089','107237','107363','107367','107475','107853','107861','107917','107953','108117','108269','108365','108401','108425','108461','108497','108539','108719','108967','109233','109309','109325','109425','109525','109625','110415','110549','110763','110809','111267','111467','111571','111665','111869','112511','112677','112789','112797','112957','113121','113133','113203','113217','113431','113519','113595','113617','113963','114157','114233','114561','114737','114807','115013','115539','115559','115923','115953','116579','116639','116729','116755','116833','117529','117727','117883','117967','118183','118313','118393','118655','118735','118747','118751','118829','118875'";

function main()
{
    global $ids_contratos;
	$dbh_ixc = getConnectionIxc();

	$stmt = $dbh_ixc->prepare("SELECT *
	FROM cliente_contrato
	WHERE id IN ($ids_contratos);");
	$stmt->execute();

	while($contrato = $stmt->fetch(PDO::FETCH_ASSOC)) {
		if ($contrato['id_carteira_cobranca'] == 1) {
			$contrato['id_carteira_cobranca'] = 6;
			$contrato['tipo_cobranca'] = 'E';
		}
		else if ($contrato['id_carteira_cobranca'] == 3) {
			$contrato['id_carteira_cobranca'] = 8;
			$contrato['tipo_cobranca'] = 'E';
		}

		$resposta = updateRow($contrato, 'cliente_contrato');

        if (!isset($resposta['type']) || $resposta['type'] !== 'success'
            || !isset($resposta['message']) || $resposta['message'] !== 'Registro atualizado com sucesso!') {
            echo "$contrato[id] - Erro ao atualizar o contrato: " . PHP_EOL;
                var_dump($resposta);
            }
        else {
            echo "$contrato[id] - Contrato atualizado com sucesso" . PHP_EOL;
        }
	}
}

main();