<?php

require_once __DIR__ . '/../../common/config.php';

function main()
{
    $dbh_noc = getConnectionNoc();
    $stmt = $dbh_noc->prepare("SELECT * FROM temp.ceps_saj_esp;");
    $stmt->execute();
    $ceps = $stmt->fetchAll(PDO::FETCH_ASSOC);

	$dbh_ixc = getConnectionIxc();
	$stmt = $dbh_ixc->prepare("SELECT cl.*,
            ci.nome AS nome_cidade
    FROM cliente cl
    INNER JOIN cliente_contrato cc ON cc.id_cliente = cl.id
    LEFT JOIN cidade ci ON ci.id = cl.cidade
    WHERE cc.status NOT IN ('D', 'I');");
	$stmt->execute();

	$i = $stmt->rowCount();
	$j = 0;
	while($cliente = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $j++;
        if($cliente['nome_cidade'] != "Espírito Santo do Pinhal" && $cliente['nome_cidade'] != "Santo Antônio do Jardim") {
            continue;
        }

        foreach($ceps as $cep) {
            if(
                trim(mb_strtolower($cliente['nome_cidade'])) === trim(mb_strtolower($cep['cidade'])) 
                && trim(mb_strtolower($cliente['endereco'])) === trim(mb_strtolower($cep['endereco'])) 
                && trim(mb_strtolower($cliente['bairro'])) === trim(mb_strtolower($cep['bairro'])) 
                && trim(mb_strtolower($cliente['cep'])) === trim(mb_strtolower($cep['cep']))

                && (
                    $cliente['endereco'] != trim($cep['endereco_novo'])
                    || $cliente['bairro'] != trim($cep['bairro_novo'])
                    || $cliente['cep'] != trim($cep['cep_novo'])
                )
                ) {

                $dadosAntigos = ['id' => $cliente['id'], 'endereco' => $cliente['endereco'], 'bairro' => $cliente['bairro'], 'cep' => $cliente['cep']];

                $cliente['endereco'] = trim($cep['endereco_novo']);
                $cliente['bairro'] = trim($cep['bairro_novo']);
                $cliente['cep'] = trim($cep['cep_novo']);

                $dadosNovos = ['id' => $cliente['id'], 'endereco' => $cliente['endereco'], 'bairro' => $cliente['bairro'], 'cep' => $cliente['cep']];

                // Remove a coluna do model, pois ela foi "criada" na query (não faz parte do model original)
                unset($cliente['nome_cidade']);

                $retorno = updateCliente($cliente);

                fileLog('--------------------------');

                if ($retorno['type'] === 'success') {
                    fileLog(date('Y-m-d H:i:s') . " - Cliente atualizado: $cliente[id]");
                    fileLog("Dados antigos: " . json_encode($dadosAntigos));
                    fileLog("Dados novos: " . json_encode($dadosNovos));
                }
                else {
                    fileLog(date('Y-m-d H:i:s') . " - Erro ao atualizar cliente $cliente[id]: " . ($retorno['message'] ?? 'Erro desconhecido'));
                }

                break;
            }
        }
        echo "\rProgresso: $j de $i";
    }
}

main();

function fileLog($message) {
    file_put_contents('./execution_clientes.log', $message . "\n", FILE_APPEND);
}