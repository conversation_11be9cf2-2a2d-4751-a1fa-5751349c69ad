<?php

require_once __DIR__ . '/../../common/config.php';

function main()
{
    $dbh_noc = getConnectionNoc();
    $stmt = $dbh_noc->prepare("SELECT * FROM temp.ceps_saj_esp;");
    $stmt->execute();
    $ceps = $stmt->fetchAll(PDO::FETCH_ASSOC);

	$dbh_ixc = getConnectionIxc();
	$stmt = $dbh_ixc->prepare("SELECT ru.*,
            ci.nome AS nome_cidade
        FROM radusuarios ru
        INNER JOIN cliente_contrato cc ON cc.id = ru.id_contrato
        LEFT JOIN cidade ci ON ci.id = ru.cidade
        WHERE cc.status NOT IN ('D', 'I');");
	$stmt->execute();

	$i = $stmt->rowCount();
	$j = 0;
	while($login = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $j++;
        if($login['nome_cidade'] != "Espírito Santo do Pinhal" && $login['nome_cidade'] != "Santo Antônio do Jardim") {
            continue;
        }

        foreach($ceps as $cep) {
            if(
                trim(mb_strtolower($login['nome_cidade'])) === trim(mb_strtolower($cep['cidade'])) 
                && trim(mb_strtolower($login['endereco'])) === trim(mb_strtolower($cep['endereco'])) 
                && trim(mb_strtolower($login['bairro'])) === trim(mb_strtolower($cep['bairro'])) 
                && trim(mb_strtolower($login['cep'])) === trim(mb_strtolower($cep['cep']))

                && (
                    $login['endereco'] != trim($cep['endereco_novo'])
                    || $login['bairro'] != trim($cep['bairro_novo'])
                    || $login['cep'] != trim($cep['cep_novo'])
                )
                ) {

                $dadosAntigos = ['id' => $login['id'], 'endereco' => $login['endereco'], 'bairro' => $login['bairro'], 'cep' => $login['cep']];

                $login['endereco'] = trim($cep['endereco_novo']);
                $login['bairro'] = trim($cep['bairro_novo']);
                $login['cep'] = trim($cep['cep_novo']);

                $dadosNovos = ['id' => $login['id'], 'endereco' => $login['endereco'], 'bairro' => $login['bairro'], 'cep' => $login['cep']];

                // Remove a coluna do model, pois ela foi "criada" na query (não faz parte do model original)
                unset($login['nome_cidade']);

                $retorno = updateRadusuario($login);

                fileLog('--------------------------');

                if ($retorno['type'] === 'success') {
                    fileLog(date('Y-m-d H:i:s') . " - Login atualizado: $login[id]");
                    fileLog("Dados antigos: " . json_encode($dadosAntigos));
                    fileLog("Dados novos: " . json_encode($dadosNovos));
                }
                else {
                    fileLog(date('Y-m-d H:i:s') . " - Erro ao atualizar login $login[id]: " . ($retorno['message'] ?? 'Erro desconhecido'));
                }

                break 2;
            }
        }
        echo "\rProgresso: $j de $i";
    }
}

main();

function fileLog($message) {
    file_put_contents('./execution_logins.log', $message . "\n", FILE_APPEND);
}