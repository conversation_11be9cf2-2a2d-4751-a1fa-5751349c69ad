<?php

require_once __DIR__ . '/../../common/config.php';

function main()
{
    $dbh_noc = getConnectionNoc();
    $stmt = $dbh_noc->prepare("SELECT * FROM temp.ceps_saj_esp;");
    $stmt->execute();
    $ceps = $stmt->fetchAll(PDO::FETCH_ASSOC);

	$dbh_ixc = getConnectionIxc();
	$stmt = $dbh_ixc->prepare("SELECT cc.*,
            ci.nome AS nome_cidade
    FROM cliente_contrato cc
    LEFT JOIN cidade ci ON ci.id = cc.cidade
    WHERE cc.status NOT IN ('D', 'I');");
	$stmt->execute();

	$i = $stmt->rowCount();
	$j = 0;
	while($contrato = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $j++;
        if($contrato['nome_cidade'] != "Espírito Santo do Pinhal" && $contrato['nome_cidade'] != "Santo Antônio do Jardim") {
            continue;
        }

        foreach($ceps as $cep) {
            if(
                trim(mb_strtolower($contrato['nome_cidade'])) === trim(mb_strtolower($cep['cidade'])) 
                && trim(mb_strtolower($contrato['endereco'])) === trim(mb_strtolower($cep['endereco'])) 
                && trim(mb_strtolower($contrato['bairro'])) === trim(mb_strtolower($cep['bairro'])) 
                && trim(mb_strtolower($contrato['cep'])) === trim(mb_strtolower($cep['cep']))

                && (
                    $contrato['endereco'] != trim($cep['endereco_novo'])
                    || $contrato['bairro'] != trim($cep['bairro_novo'])
                    || $contrato['cep'] != trim($cep['cep_novo'])
                )
                ) {

                $dadosAntigos = ['id' => $contrato['id'], 'endereco' => $contrato['endereco'], 'bairro' => $contrato['bairro'], 'cep' => $contrato['cep']];

                $contrato['endereco'] = trim($cep['endereco_novo']);
                $contrato['bairro'] = trim($cep['bairro_novo']);
                $contrato['cep'] = trim($cep['cep_novo']);

                $dadosNovos = ['id' => $contrato['id'], 'endereco' => $contrato['endereco'], 'bairro' => $contrato['bairro'], 'cep' => $contrato['cep']];

                // Remove a coluna do model, pois ela foi "criada" na query (não faz parte do model original)
                unset($contrato['nome_cidade']);

                $retorno = updateContrato($contrato);

                fileLog('--------------------------');

                if ($retorno['type'] === 'success') {
                    fileLog(date('Y-m-d H:i:s') . " - Contrato atualizado: $contrato[id]");
                    fileLog("Dados antigos: " . json_encode($dadosAntigos));
                    fileLog("Dados novos: " . json_encode($dadosNovos));
                }
                else {
                    fileLog(date('Y-m-d H:i:s') . " - Erro ao atualizar contrato $contrato[id]: " . ($retorno['message'] ?? 'Erro desconhecido'));
                }

                break;
            }
        }
        echo "\rProgresso: $j de $i";
    }
}

main();

function fileLog($message) {
    file_put_contents('./execution_contratos.log', $message . "\n", FILE_APPEND);
}