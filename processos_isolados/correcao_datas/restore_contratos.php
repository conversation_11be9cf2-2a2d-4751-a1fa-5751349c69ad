<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/Logger.php';

// Read and decode JSON files into PHP arrays
$ids_contratos_json = file_get_contents('ids_contratos.json');
$ids_contratos = json_decode($ids_contratos_json, true);


$colunas_json = file_get_contents('colunas_contratos.json');
$colunas = json_decode($colunas_json, true);

$dbh_ixc = getConnectionIxc();

$ids_contratos_placeholder = implode(',', array_fill(0, count($ids_contratos), '?'));
$contratos_stmt = $dbh_ixc->prepare("SELECT * FROM cliente_contrato WHERE id IN ($ids_contratos_placeholder);");
$contratos_stmt->execute($ids_contratos);

// $contratos_stmt = $dbh_ixc->prepare("SELECT * FROM cliente_contrato WHERE id IN (876);");
// $contratos_stmt->execute();

$contratos = $contratos_stmt->fetchAll(PDO::FETCH_ASSOC);

$total = sizeof($contratos);
$concluidos = 0;
foreach($contratos as $contrato) {
    restoreDates($contrato);
    // break;
    $concluidos++;
    echo "\rProgresso: $concluidos de $total";
}

function restoreDates($contrato)
{
    global $colunas;
    $id_contrato = $contrato['id'];

    $contrato_antigo = $contrato;

    $logs = getContratoLog($id_contrato);
    foreach ($colunas as $coluna) {
        foreach ($logs as $alteracao) {
            $campos =  json_decode($alteracao['campos']);
            if (!$campos)
                continue;
            foreach ($campos as $chave => $valor) {
                if (
                    $chave == $coluna
                    && trim($valor) != ''
                    && !str_starts_with($valor, '0')
                    && !str_starts_with($valor, '-')
                    && (preg_match('/^\d{4}-\d{2}-\d{2}$/', $valor) === 1)
                    && (
                        trim($contrato[$coluna]) == ''
                        || str_starts_with($contrato[$coluna], '0')
                        || str_starts_with($contrato[$coluna], '-')
                        || (preg_match('/^\d{4}-\d{2}-\d{2}$/', $contrato[$coluna]) !== 1)
                    )
                ) {
                    $contrato[$coluna] = $valor;
                    continue 3;
                }
            }
        }
    }

    fileLog('--------------------');
    fileLog("contrato $contrato[id]");
    fileLog('--');
    foreach ($colunas as $coluna) {
        if ($contrato_antigo[$coluna] != $contrato[$coluna]) {
            fileLog("coluna $coluna");
            fileLog("antigo: $contrato_antigo[$coluna]");
            fileLog("novo: $contrato[$coluna]");
            fileLog('--');
        }
    }
    $retorno = updateContrato($contrato);
    if ($retorno['type'] === 'success') {
        fileLog("$contrato[id] - sucesso");
    }
    else {
        fileLog("$contrato[id] - erro: " . isset($retorno['message']) ? $retorno['message'] : 'ERRO DESCONHECIDO');
    }
    fileLog('--------------------');
}


function getContratoLog($id_contrato)
{
    $api = getIxcApi();
    $params = array(
        'qtype' => 'ixc_logs.id_tabela',
        'query' => '',
        'oper' => '=',
        'sortname' => 'ixc_logs.data',
        'sortorder' => 'desc',
        'page' => '1',
        'rp' => '9000',
        'grid_param' => json_encode([
            [
                'TB' => 'ixc_logs.tabela',
                'OP' => '=',
                'P' => 'cliente_contrato'
            ],
            [
                'TB' => 'ixc_logs.id_tabela',
                'OP' => '=',
                'P' => $id_contrato
            ]
        ])
    );

    $api->get('ixc_logs', $params);
    $retorno = $api->getRespostaConteudo(true);

    $registros = $retorno['registros'];

    return $registros;
}

function fileLog($message) {
    file_put_contents('./restore_contratos.log', $message . "\n", FILE_APPEND);
}