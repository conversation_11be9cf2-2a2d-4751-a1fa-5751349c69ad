<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/Logger.php';

// Read and decode JSON files into PHP arrays
$ids_clientes_json = file_get_contents('ids_clientes.json');
$ids_clientes = json_decode($ids_clientes_json, true);


$colunas_json = file_get_contents('colunas_clientes.json');
$colunas = json_decode($colunas_json, true);

$dbh_ixc = getConnectionIxc();

$ids_clientes_placeholder = implode(',', array_fill(0, count($ids_clientes), '?'));
$clientes_stmt = $dbh_ixc->prepare("SELECT * FROM cliente WHERE id IN ($ids_clientes_placeholder);");
$clientes_stmt->execute($ids_clientes);

// $clientes_stmt = $dbh_ixc->prepare("SELECT * FROM cliente WHERE id IN (99603);");
// $clientes_stmt->execute();

$clientes = $clientes_stmt->fetchAll(PDO::FETCH_ASSOC);

$total = sizeof($clientes);
$concluidos = 0;
foreach($clientes as $cliente) {
    restoreDates($cliente);
    $concluidos++;
    echo "\rProgresso: $concluidos de $total";
    // break;
}

function restoreDates($cliente)
{
    global $colunas;
    $id_cliente = $cliente['id'];

    $cliente_antigo = $cliente;

    $logs = getClienteLog($id_cliente);
    foreach ($colunas as $coluna) {
        foreach ($logs as $alteracao) {
            $campos =  json_decode($alteracao['campos']);
            if (!$campos)
                continue;
            foreach ($campos as $chave => $valor) {
                if (
                    $chave == $coluna
                    && trim($valor) != ''
                    && !str_starts_with($valor, '0')
                    && !str_starts_with($valor, '-')
                    && (preg_match('/^\d{4}-\d{2}-\d{2}$/', $valor) === 1)
                    && (
                        trim($cliente[$coluna]) == ''
                        || str_starts_with($cliente[$coluna], '0')
                        || str_starts_with($cliente[$coluna], '-')
                        || (preg_match('/^\d{4}-\d{2}-\d{2}$/', $cliente[$coluna]) !== 1)
                        )
                ) {
                    $cliente[$coluna] = $valor;
                    continue 3;
                }
            }
        }
    }
    
    fileLog('--------------------');
    fileLog("cliente $cliente[id]");
    fileLog('--');
    foreach ($colunas as $coluna) {
        if ($cliente_antigo[$coluna] != $cliente[$coluna]) {
            fileLog("coluna $coluna");
            fileLog("antigo: $cliente_antigo[$coluna]");
            fileLog("novo: $cliente[$coluna]");
            fileLog('--');
        }
    }
    $retorno = updateCliente($cliente);
    // var_dump($retorno);
    if ($retorno['type'] === 'success') {
        fileLog("$cliente[id] - sucesso");
    }
    else {
        fileLog("$cliente[id] - erro: " . isset($retorno['message']) ? $retorno['message'] : 'ERRO DESCONHECIDO');
    }
    fileLog('--------------------');
}


function getClienteLog($id_cliente)
{
    $api = getIxcApi();
    $params = array(
        'qtype' => 'ixc_logs.id_tabela',
        'query' => '',
        'oper' => '=',
        'sortname' => 'ixc_logs.data',
        'sortorder' => 'desc',
        'page' => '1',
        'rp' => '9000',
        'grid_param' => json_encode([
            [
                'TB' => 'ixc_logs.tabela',
                'OP' => '=',
                'P' => 'cliente'
            ],
            [
                'TB' => 'ixc_logs.id_tabela',
                'OP' => '=',
                'P' => $id_cliente
            ]
        ])
    );

    $api->get('ixc_logs', $params);
    $retorno = $api->getRespostaConteudo(true);

    $registros = $retorno['registros'];

    return $registros;
}

function fileLog($message) {
    file_put_contents('./restore_clientes.log', $message . "\n", FILE_APPEND);
}