<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/Logger.php';

$dbh_recovery = getConnectionRecovery();

$sql = "SELECT cb.*
FROM cliente c
LEFT JOIN cliente_bkp cb ON cb.id = c.id
WHERE
    c.data_nascimento < cb.data_nascimento
    OR c.crm_data_vencemos < cb.crm_data_vencemos
    OR c.crm_data_perdemos < cb.crm_data_perdemos
    OR c.crm_data_novo < cb.crm_data_novo
    OR c.crm_data_sondagem < cb.crm_data_sondagem
    OR c.crm_data_apresentando < cb.crm_data_apresentando
    OR c.crm_data_negociando < cb.crm_data_negociando
    OR c.crm_data_abortamos < cb.crm_data_abortamos
    OR c.crm_data_sem_porta_disponivel < cb.crm_data_sem_porta_disponivel
    OR c.crm_data_sem_viabilidade < cb.crm_data_sem_viabilidade";

// Execute the query
$stmt = $dbh_recovery->prepare($sql);
$stmt->execute();

$rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

$replacingColumns = [
    "data_nascimento",
    "crm_data_vencemos",
    "crm_data_perdemos",
    "crm_data_novo",
    "crm_data_sondagem",
    "crm_data_apresentando",
    "crm_data_negociando",
    "crm_data_abortamos",
    "crm_data_sem_porta_disponivel",
    "crm_data_sem_viabilidade",
];
$concluidos = 0;
foreach ($rows as $clienteBkp) {
    $clienteAtual = getCliente($clienteBkp['id']);

    fileLog("Cliente: $clienteAtual[id]");

    $changedColumns = false;
    foreach ($replacingColumns as $column) {
        $dataAtualTs = strtotime($clienteAtual[$column]);
        $dataBkpTs = strtotime($clienteBkp[$column]);

        if ($dataAtualTs >= $dataBkpTs)
            continue;

        $novaData = date('Y-m-d', $dataBkpTs);

        $clienteAtual[$column] = $novaData;

        fileLog("$column atual: " . date('Y-m-d', $dataAtualTs));

        fileLog("$column nova: $novaData");

        $changedColumns = true;
    }

    if (!$changedColumns) {
        fileLog("Nenhuma alteração encontrada no cliente $clienteAtual[id]");
        fileLog("------------");
        continue;
    }

    $retorno = updateCliente($clienteAtual);
    if ($retorno['type'] === 'success') {
        fileLog("Atualizado com sucesso");
    }
    else {
        fileLog("Erro ao atualizar: " . isset($retorno['message']) ? $retorno['message'] : 'ERRO DESCONHECIDO');
    }
    
    fileLog("------------");
    $concluidos++;
    echo "\rProgresso: $concluidos de $total";
}

function fileLog($message) {
    file_put_contents('./restore_c_from_bkp.log', $message . "\n", FILE_APPEND);
}