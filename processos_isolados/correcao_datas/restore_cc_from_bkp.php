<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/Logger.php';

$dbh_recovery = getConnectionRecovery();

$sql = "SELECT ccb.*
FROM cliente_contrato cc
LEFT JOIN cliente_contrato_bkp ccb ON ccb.id = cc.id
WHERE
    cc.nao_bloquear_ate < ccb.nao_bloquear_ate
    OR cc.nao_avisar_ate < ccb.nao_avisar_ate
    OR cc.data_cadastro_sistema < ccb.data_cadastro_sistema
    OR cc.data_renovacao < ccb.data_renovacao
    OR cc.data_ativacao < ccb.data_ativacao
    OR cc.data_expiracao < ccb.data_expiracao;";

// Execute the query
$stmt = $dbh_recovery->prepare($sql);
$stmt->execute();

$rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
$total = sizeof($rows);

$replacingColumns = [
    'nao_bloquear_ate',
    'nao_avisar_ate',
    'data_cadastro_sistema',
    'data_expiracao',
    'data_renovacao',
    'data_ativacao'
];
$concluidos = 0;
foreach ($rows as $contratoBkp) {
    $contratoAtual = getContrato($contratoBkp['id']);

    fileLog("Contrato: $contratoAtual[id]");

    $changedColumns = false;
    foreach ($replacingColumns as $column) {
        $dataAtualTs = strtotime($contratoAtual[$column]);
        $dataBkpTs = strtotime($contratoBkp[$column]);

        if ($dataAtualTs >= $dataBkpTs)
            continue;

        $novaData = date('Y-m-d', $dataBkpTs);

        $contratoAtual[$column] = $novaData;

        fileLog("$column atual: " . date('Y-m-d', $dataAtualTs));

        fileLog("$column nova: $novaData");

        $changedColumns = true;
    }

    if (!$changedColumns) {
        fileLog("Nenhuma alteração encontrada no contrato $contratoAtual[id]");
        fileLog("------------");
        continue;
    }

    $retorno = updateContrato($contratoAtual);
    if ($retorno['type'] === 'success') {
        fileLog("Atualizado com sucesso");
    }
    else {
        fileLog("Erro ao atualizar: " . isset($retorno['message']) ? $retorno['message'] : 'ERRO DESCONHECIDO');
    }
    
    fileLog("------------");
    $concluidos++;
    echo "\rProgresso: $concluidos de $total";
}

function fileLog($message) {
    file_put_contents('./restore_cc_from_bkp.log', $message . "\n", FILE_APPEND);
}