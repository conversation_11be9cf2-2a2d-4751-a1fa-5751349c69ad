<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/Logger.php';

$dbh = getConnectionIxc();

$stmt = $dbh->prepare("SELECT * FROM fn_areceber WHERE data_emissao = '2022-01-26' AND data_vencimento = '2022-03-02' LIMIT 1;");
$stmt->execute();

$faturas = $stmt->fetchAll(PDO::FETCH_ASSOC);

$fatura = $faturas[0];

echo $fatura['id'];

$api = getIxcApi();
$fatura['data_vencimento'] = '2022-02-28';
$api->put('fn_areceber', $fatura, $fatura['id']);
$retorno = $api->getRespostaConteudo(false);// false para json | true para array

var_dump($retorno);