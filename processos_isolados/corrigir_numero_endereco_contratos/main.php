<?php

require_once __DIR__ . '/../../common/config.php';

function main()
{
	$dbh_ixc = getConnectionIxc();

	// $stmt = $dbh_ixc->prepare("SELECT *
	// FROM cliente_contrato
	// WHERE id_filial = 7
	// 	AND id_carteira_cobranca IN (4, 11)
	// 	AND (
	// 		tipo_doc_opc = 11
	// 		OR tipo_doc_opc2 = 114
	// 		OR tipo_doc_opc3 = 113
	// 		)
	// AND status NOT IN ('I', 'D');");
	// $stmt->execute();

	$stmt = $dbh_ixc->prepare("SELECT *
	FROM cliente_contrato
	WHERE numero NOT REGEXP '^[-+]?[0-9]*\.?[0-9]+([eE][-+]?[0-9]+)?$'
		AND UPPER(numero) LIKE '%S/N%'
		AND status NOT IN ('D', 'I');");
	$stmt->execute();

	$i = $stmt->rowCount();
	$j = 0;
	while($contrato = $stmt->fetch(PDO::FETCH_ASSOC)) {
		// Controle de limite de repetições, para testes
		// if($j++ == 1)
		// 	break;

		$contrato['numero'] = 'SN';

		$resposta = updateRow($contrato, 'cliente_contrato');

		// var_dump($resposta);
		echo --$i . ' - ' . $contrato['id'] . PHP_EOL;
	}
}

main();