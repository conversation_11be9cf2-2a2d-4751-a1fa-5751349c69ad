<?php

require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/utils.php';
require_once __DIR__ . '/../../common/Logger.php';

$api = getIxcApi();

main();

function main() {
    $dbh_noc = getConnectionNoc();

    $stmt = $dbh_noc->prepare("SELECT cc.id
        FROM fdwtables.ixc_cliente_contrato cc
        WHERE cc.status NOT IN ('D', 'I')
            AND cc.id NOT IN (
                SELECT id_contrato
                FROM temp.contratos_data_expiracao
            )");
    $stmt->execute();

    $ids_contratos = $stmt->fetchAll(PDO::FETCH_COLUMN);

    foreach($ids_contratos as $id_contrato) {
        
        $dados = getLastDataExpiracao($id_contrato);

        try {
            $stmt = $dbh_noc->prepare("INSERT INTO temp.contratos_data_expiracao (id_log, id_contrato, data_ultima_alteracao, data_expiracao, operador) VALUES (?, ?, ?, ?, ?);");
            $stmt->execute([
                $dados['id_log'],
                $id_contrato,
                $dados['data_ultima_alteracao'],
                $dados['nova_data_expiracao'],
                $dados['operador']
            ]);
        }
        catch(Exception $e) {
            echo "$id_contrato, ";
        }
    }
}

function getLastDataExpiracao($id_contrato) {
    global $api;

    $params = array(
        'qtype' => 'ixc_logs.id',
        'query' => '',
        'oper' => '!=',
        'sortname' => 'ixc_logs.id',
        'sortorder' => 'desc',
        'page' => '1',
        'rp' => '90000',
        'grid_param' => json_encode([
            [
                'TB' => 'ixc_logs.tabela',
                'OP'=>'=',
                'P'=>'cliente_contrato'
            ],
            [
                'TB' => 'ixc_logs.id_tabela',
                'OP'=>'=',
                'P'=>$id_contrato
            ]
    ]));
    $api->get('ixc_logs', $params);
    $retorno = $api->getRespostaConteudo(true); // false para json | true para array
    
    $lastDataExpiracao = ['id_log' => 0];
    foreach($retorno['registros'] as $registro) {
        $campos = json_decode($registro['campos'], true);
    
        if ($registro['id'] < $lastDataExpiracao['id_log']
            || !isset($campos['data_expiracao'])
            || $campos['data_expiracao'] === '')
            continue;

        $lastDataExpiracao = [
            'id_log' => $registro['id'],
            'operador' => utf8_encode($registro['operador']),
            'data_ultima_alteracao' => $registro['data'],
            'nova_data_expiracao' => $campos['data_expiracao']
        ];
    }

    return $lastDataExpiracao;
}