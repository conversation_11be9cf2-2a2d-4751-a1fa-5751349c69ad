<?php
require_once __DIR__ . '/../../common/config.php';

function main()
{
	$dbh_ixc = getConnectionIxc();

	$stmt = $dbh_ixc->prepare("SELECT *
	FROM cliente_contrato
	WHERE id_filial = 13
		AND id_carteira_cobranca IN (5, 14)
		AND tipo_doc_opc = 12
		AND tipo_doc_opc2 = 122
		AND tipo_doc_opc3 = 121
	AND status NOT IN ('I', 'D');");
	$stmt->execute();

	while($contrato = $stmt->fetch(PDO::FETCH_ASSOC)) {
		// Correções de verificações da API do IXC (buga se enviar os valores como 0):
		if($contrato['avalista_1'] == 0 && $contrato['avalista_2'] == 0) {
			$contrato['avalista_1'] = '';
			$contrato['avalista_2'] = '';
		}
		if ($contrato['motivo_cancelamento'] == 0)
			$contrato['motivo_cancelamento'] = ' ';

		// ----------------- <PERSON><PERSON> Elias: -------------------

		// Alterar filial de 13 pra 2
		$contrato['id_filial'] = 2;

		if ($contrato['id_carteira_cobranca'] == 5) {
			$contrato['id_carteira_cobranca'] = 1;
			$contrato['tipo_cobranca'] = 'P';
		}
		else if ($contrato['id_carteira_cobranca'] == 14) {
			$contrato['id_carteira_cobranca'] = 6;
			$contrato['tipo_cobranca'] = 'E';
		}

		// Alterar tipo_doc_opc de 12 pra 10
		$contrato['tipo_doc_opc'] = 10;

		// Alterar tipo_doc_opc2 de 122 pra 104
		$contrato['tipo_doc_opc2'] = 104;

		// Alterar tipo_doc_opc3 de 121 pra 128
		$contrato['tipo_doc_opc3'] = 128;

		$resposta = updateRow($contrato, 'cliente_contrato');

		var_dump($resposta);
	}
}

main();