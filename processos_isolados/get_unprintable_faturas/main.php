<?php
$rp = '/../../';
require_once __DIR__ . $rp . 'common/config.php';
require_once __DIR__ . $rp . 'common/Logger.php';

$api = getIxcApi();

$dbh = getConnectionIxc();

$stmt = $dbh->prepare("SELECT ar.id, cl.cnpj_cpf
FROM fn_areceber ar
	INNER JOIN cliente cl ON cl.id = ar.id_cliente
WHERE ar.status = 'A'
	AND ar.data_vencimento BETWEEN '2022-01-01' AND '2022-01-31'
ORDER BY ar.data_vencimento DESC;");
$stmt->execute();

$faturas = $stmt->fetchAll(PDO::FETCH_ASSOC);

foreach($faturas as $fatura) {

	// array de parâmetros do método
	$params = array(
		'boletos' => $fatura['id'],
		'juro' => 'N', // para cálculo de júro
		'multa' => 'N', // para cálculo de multa
		'atualiza_boleto' => 'N', // para atualizar o boleto
		'tipo_boleto' => 'arquivo', // tipo de método que será executado
		'base64' => 'S' // para retornar arquivo em formato base64 (else = binário)
	);

	// execução do método GET API
	$api->get('get_boleto', $params);
	$str_pdf_base64 = $api->getRespostaConteudo(false); // false para retorno em JSON e true para retorno em ARRAY PHP
	// printando resultado

	if (strpos($str_pdf_base64, 'A carteira') !== false) echo "Fatura bugada: $fatura[id]" . PHP_EOL;
}