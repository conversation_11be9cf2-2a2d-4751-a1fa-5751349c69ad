<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/Logger.php';

main();

function main() {
	$dbh_ixc = getConnectionIxc();
	$dbh_radius = getConnectionRadius();

	$stmt = $dbh_ixc->prepare("SELECT
	ru.*,
	(CASE
		WHEN ru.ativo = 'S' THEN 1
		ELSE 0
	END) AS cc_ativo,
	(CASE
		WHEN rpr.descricao != '' AND rpr.descricao IS NOT NULL THEN rpr.descricao
		WHEN ru.endereco_padrao_cliente = 'S' OR (ru.endereco_padrao_cliente = 'N' AND (ru.cidade = 0 OR ru.cidade IS NULL)) THEN
		cl.cidade
		ELSE ru.cidade
	END) AS cc_escopo,
		ci1.nome AS cc_cidade_login,
		ci2.nome AS cc_cidade_cliente
	FROM radusuarios ru
	INNER JOIN cliente cl ON cl.id = ru.id_cliente 
	LEFT JOIN cidade ci1 ON ci1.id = ru.cidade	 
	LEFT JOIN cidade ci2 ON ci2.id = cl.cidade
	LEFT JOIN radpop_radio rpr ON rpr.id = ru.id_transmissor
	LEFT JOIN radpop rp ON rp.id = rpr.id_pop
	ORDER BY id DESC
	LIMIT 1000;");
	$stmt->execute();
	$radusuarios = $stmt->fetchAll(PDO::FETCH_ASSOC);

	foreach($radusuarios as $radusuario) {
		$id_plano_ixc = $radusuario['id_grupo'];
		$id_cad_planos = getCadPlanosId($id_plano_ixc);
		
		if(!$id_cad_planos)
		echo $id_plano_ixc, PHP_EOL;
	}

}








function getCadPlanosId($id_plano_ixc)
{
	// Planos que estao com velocidade zerada na tabela do IXC
	if (in_array($id_plano_ixc, [
		994, 995, 996, 997, 1006
	])) {
		$Logger->write([
			'flag' => 'CRITICAL',
			'message' => "erro ao atualizar usuário: plano ID \"$id_plano_ixc\" cadastrado sem velocidade"
		]);
		return false;
	}

	$dbh_radius = getConnectionRadius();

	$stmt = $dbh_radius->prepare("SELECT r.id AS id_cad_planos FROM 
		temp.ixc_planos i
	INNER JOIN cad_planos r ON
		plano2bits(i.download) = plano2bits(r.down)
		AND plano2bits(i.upload) = plano2bits(r.up)
	WHERE i.id = ?
	LIMIT 1;");
	$query = $stmt->execute(array($id_plano_ixc));

	$id_cad_planos = $stmt->fetchColumn();

	return $id_cad_planos;
}