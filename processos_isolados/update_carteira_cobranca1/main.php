<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/utils.php';
require_once __DIR__ . '/../../common/Logger.php';

$dbh_ixc = getConnectionIXC();
$dbh_noc = getConnectionNoc();
$api = getIxcApi();

$stmt = $dbh_noc->prepare("SELECT * FROM temp.boletos_pagos_pix;");
$stmt->execute();
$rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

$erros = 0;
$sucesso = 0;
$processados = 0;
$total = sizeof($rows);

foreach ($rows as $row) {
    try {
        $cliente = getCliente($row['id_cliente']);
        $contrato = getContrato($row['id_contrato']);

        $contrato['tipo_cobranca'] = 'E';
        $contrato['id_carteira_cobranca'] = 19;
        $update = updateRow($contrato, 'cliente_contrato');
        if ($update['type'] !== 'success')
            throw new Exception("Erro ao atualizar a carteira de cobrança do contrato $contrato[id]: $update[message]");

        $atendimento = [
            'tipo' => 'C',
            'id_cliente' => $cliente['id'],
            'id_contrato' => $contrato['id'],
            'id_assunto' => 4543,
            'prioridade' => 'M',
            'id_ticket_origem' => 'I',
            'interacao_pendente' => 'N',
            'titulo' => 'AJUSTE DE CARTEIRA DE COBRANCA PARA MODOBANK - PIX', // Descrição assunto
            'id_ticket_setor' => 5, // Departamento
            'menssagem' => 'AJUSTE DE CARTEIRA DE COBRANCA PARA MODOBANK - PIX.', // Descrição
            'status' => 'T',
            'su_status' => 'S' // Status
        ];
        $api->post('su_ticket', $atendimento);
        $retorno = $api->getRespostaConteudo(true);
        if ($retorno['type'] !== 'success')
            throw new Exception("Erro ao gerar atendimento para o contrato $contrato[id]: $retorno[message]");

        writeLog("Carteira de cobrança alterada e atendimento registrado para o contrato $contrato[id]");
        $sucesso++;
    } catch (Exception $e) {
        $erros++;
        writeLog($e->getMessage());
    }

    $processados++;

    echo "Sucesso: $sucesso | Erros: $erros | Processados: $processados/$total (" . number_format(($processados / $total) * 100, 2) . "%)" . PHP_EOL;
}

// --------------------------------------------

function writeLog($str)
{
    $now = date('d/m/Y H:i:s');
    $str = '[' . $now . ']: ' . $str . PHP_EOL;
    file_put_contents(__DIR__ . '/logger.log', $str, FILE_APPEND);
}
