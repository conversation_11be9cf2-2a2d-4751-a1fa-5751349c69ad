<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/utils.php';
require_once __DIR__ . '/../../common/Logger.php';

function main()
{
	$dbh_ixc = getConnectionIXC();
	
	$stmt = $dbh_ixc->prepare("SELECT * FROM cliente WHERE id IN (263,274,337,377,642,1425,1815,1887,3016,3045,3162,3247,4170,4495,4577,5276,5898,6278,6415,6480,6517,6648,7516,7647,7709,7916,8072,8239,8894,8902,8943,9091,9154,9209,9359,10147,10320,10630,11199,11529,11768,11774,11827,11851,12493,12635,12660,13412,13980,15223,15327,15446,16071,16381,16836,16872,16967,17047,17103,17258,17961,18198,18760,19413,19810,19866,19870,20421,20878,20996,21050,21092,21189,21201,21207,21539,21604,21658,21877,22062,22430,22820,22956,22998,23092,23800,24004,24325,24417,24617,24685,24924,24935,24950,24963,25066,25149,25456,25459,25560,25589,25598,25842,26051,26104,26131,26255,26296,26637,26641,26752,26768,26803,27087,27163,27686,27957,28064,28117,28131,28154,28686,28823,28922,29549,29620,29779,29904,30062,30185,30392,30488,30597,30732,30738,30761,30919,31337,31388,31392,31507,31717,31757,32033,32094,32272,32286,32295,32334,32365,32561,32576,32602,32836,33226,33258,33363,33487,33628,33748,33801,33806,33841,34013,34243,34337,34598,34662,34828,35278,35369,35531,36057,36138,36159,36354,36580,36614,36731,36778,36877,37028,37486,37627,37771,37790,38070,38134,38198,38374,38410,38414,38461,38627,38799,38804,38805,38888,38918,39223,39225,39266,39472,39609,39930,39981,40010,40165,40248,40481,40746,40767,40823,40839,41229,41319,41359,41485,41497,41510,41522,41592,41605,41633,41690,41702,41736,41902,42165,42277,42597,42916,42996,43025,43365,43422,43484,43830,43914,43935,43988,44029,44359,44503,44533,44597,44675,44768,44780,44869,44914,44965,44990,45062,45273,45488,45562,46277,46309,46356,46504,46517,46587,46810,46812,46956,46982,47197,47216,47236,47297,47365,47375,47530,47563,47573,47785,48050,48283,48369,48411,48431,48448,48468,48494,48646,48833,48894,48958,49164,49184,49301,49390,49880,49884,49929,50150,50417,50454,50505,50829,50948,51087,51198,51282,51352,51380,51574,51594,51745,51846,51903,51928,52214,52260,52299,52411,52521,52551,52657,52686,52864,52952,52968,53127,53200,53244,53287,53288,53388,53648,53672,53673,53739,53751,53880,53900,54074,54149,54159,54194,54410,54543,54956,55566,55603,56403,56421,56517,56520,56944,56947,57166,57378,57405,58075,58600,58683,58817,58833,58893,59145,59365,59571,59645,59689,59915,59957,59985,60171,60297,60305,60359,60659,60861,60881,60933,60935,61043,61065,61227,61229,61265,61337,61547,61771,61797,61861,61889,61997,62077,62177,62343);");
	$stmt->execute();
	
	$clientes = $stmt->fetchAll(PDO::FETCH_ASSOC);

	foreach($clientes as $cliente) {
		echo "Atualizando cliente ID $cliente[id]" . PHP_EOL;
		echo "Senha antiga: $cliente[senha]" . PHP_EOL;
		$cliente['senha'] = preg_replace('/[^0-9]/', '', $cliente['cnpj_cpf']);
		echo "Nova senha: $cliente[senha]" . PHP_EOL;
		$resposta = updateCliente($cliente);
		echo PHP_EOL;
	}
}

main();