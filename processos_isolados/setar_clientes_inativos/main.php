<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/utils.php';

function main() {
	$dbh = getConnectionIxc();

	$stmt = $dbh->prepare("SELECT *
		FROM cliente
		WHERE ativo = 'S'
			AND id NOT IN
				(SELECT id_cliente FROM cliente_contrato WHERE status NOT IN ('I', 'D'));");
	$stmt->execute();

	$clientes = $stmt->fetchAll(PDO::FETCH_ASSOC);

	$i=0;
	foreach($clientes as $cliente) {
		$cliente['ativo'] = 'N';
		$resposta = updateCliente($cliente);

		// if(strpos($resposta['message'], 'E-mail') !== false){
		// 	$cliente['email'] = '<EMAIL>';
		// 	updateCliente($cliente);
		// }

		var_dump($resposta);
		if(++$i == 2)
		break;
	}
}

main();
