<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/Logger.php';

main();

function main() {
	$dbh_noc = getConnectionNoc();
	$dbh_ixc = getConnectionIxc();
	
	$query = "SELECT * FROM public.address_recovery;";
	$stmt = $dbh_noc->prepare($query);
	$stmt->execute();

	$rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

	$i = 1;
	foreach($rows as $row) {
		$campos = json_decode($row['ultimo_registro_com_endereco'], true);

		$query = "SELECT * FROM radusuarios WHERE login = ?;";
		$stmt = $dbh_ixc->prepare($query);
		$stmt->execute([$row['login']]);
		$radusuario  = $stmt->fetch(PDO::FETCH_ASSOC);

		$radusuario['endereco'] = $campos['endereco'];
		$radusuario['endereco_padrao_cliente'] = $campos['endereco_padrao_cliente'];
		$radusuario['numero'] = $campos['numero'];
		$radusuario['bairro'] = $campos['bairro'];
		$radusuario['cidade'] = $campos['cidade'];
		$radusuario['cep'] = $campos['cep'];
		$radusuario['complemento'] = $campos['complemento'];
		$radusuario['referencia'] = $campos['referencia'];
		$radusuario['id_condominio'] = $campos['id_condominio'];
		$radusuario['bloco'] = $campos['bloco'];
		$radusuario['apartamento'] = $campos['apartamento'];

		// updateRadusuario($radusuario);

		echo "endereco do $row[login]: $campos[endereco]".PHP_EOL;
	}
}