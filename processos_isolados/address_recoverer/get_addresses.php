<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/Logger.php';

main();

function main() {
	$dbh_noc = getConnectionNoc();
	$dbh_ixc = getConnectionIxc();

	$query = "SELECT * FROM radusuarios WHERE endereco_padrao_cliente = 'N' AND (endereco = '' OR endereco IS NULL);";

	$stmt = $dbh_ixc->prepare($query);
	$stmt->execute();

	$result = $stmt->fetchAll(PDO::FETCH_ASSOC);

	$i = 1;
	foreach($result as $radusuario) {

		$query = "SELECT l.data, u.nome AS operador,
		TRIM(BOTH '\"' FROM json_extract(l.campos, '$.endereco')) AS endereco,
		TRIM(BOTH '\"' FROM json_extract(l.campos, '$.numero')) AS numero,
		l.campos FROM radusuarios r
		INNER JOIN ixc_logs l ON l.tabela = 'radusuarios' AND l.id_tabela = r.id
		WHERE login = ?
		ORDER BY l.data DESC";

		$stmt = $dbh_ixc->prepare($query);
		$stmt->execute([$radusuario['login']]);
		$last_edits = $stmt->fetchAll(PDO::FETCH_ASSOC);

		$last_operador = null;
		foreach($last_edits as $edit) {
			if($edit['endereco'] != '' && $last_operador == 'webservice') {
				$query = "INSERT INTO public.address_recovery (login, ultimo_registro_com_endereco) VALUES (?, ?);";
				$stmt = $dbh_noc->prepare($query);
				$stmt->execute([$radusuario['login'], utf8_encode($edit['campos'])]);
				break;
			}
			else {
				$last_operador = $edit['operador'];
			}
		}
	}
}