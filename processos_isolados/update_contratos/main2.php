<?php

require_once __DIR__ . '/../../common/config.php';

function main()
{
	$dbh_ixc = getConnectionIxc();

	$stmt = $dbh_ixc->prepare("SELECT vs.*
FROM fn_areceber fr
INNER JOIN vd_saida vs ON vs.id = fr.id_saida
WHERE fr.data_emissao = '2022-02-28' AND fr.data_vencimento = '2022-03-30';");
	$stmt->execute();

	$faturas = $stmt->fetchAll(PDO::FETCH_ASSOC);

	var_dump($faturas[1]['id']);

	var_dump($faturas[1]['obs']);

	$faturas[1]['obs'] = 'de 01/02/2022 até 28/02/2022';

	var_dump($faturas[1]['obs']);

	// $resposta = updateRow($faturas[1], 'fn_areceber');

	// var_dump($resposta);
}

main();