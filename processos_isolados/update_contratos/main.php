<?php

require_once __DIR__ . '/../../common/config.php';

function main()
{
	$dbh_ixc = getConnectionIxc();

	// $stmt = $dbh_ixc->prepare("SELECT *
	// FROM cliente_contrato
	// WHERE id_filial = 7
	// 	AND id_carteira_cobranca IN (4, 11)
	// 	AND (
	// 		tipo_doc_opc = 11
	// 		OR tipo_doc_opc2 = 114
	// 		OR tipo_doc_opc3 = 113
	// 		)
	// AND status NOT IN ('I', 'D');");
	// $stmt->execute();

	$stmt = $dbh_ixc->prepare("SELECT * FROM cliente_contrato
	WHERE id IN (38,51,100,178,492,609,620,683,836,903,956,964,1067,1201,1362,1400,1460,1657,1729,1793,1805,2152,2204,2272,2377,2389,2416,2532,2674,2958,2998,3390,3431,3649,3794,3871,3941,4274,4362,4444,4507,4509,4519,4632,4659,4667,4817,4892,4926,4965,5143,5504,5507,5658,5758,5910,5933,5975,6038,6068,6204,6253,6351,6385,6489,6562,6604,6611,6753,6768,6788,6931,6968,7038,7072,7105,7108,7391,7412,7571,7689,7851,7875,7886,7888,8106,8370,8386,8451,8525,8637,8708,8720,8914,8939,8959,8976,9213,9269,9333,9340,9406,9566,9637,9666,9696,9764,9823,9955,9993,10021,10029,10064,10148,10159,10269,10344,10469,10477,10501,10716,10816,10840,10969,11012,11026,11070,11215,11272,11404,11410,11427,11477,11529,11575,11609,11646,11707,11904,12449,12642,12714,12728,12971,13070,13075,13353,13380,13389,13521,13660,13914,14158,14497,14604,14632,14767,14832,15116,15123,15197,15200,15381,16042,16199,16313,16324,16426,16542,16767,16842,17097,17605,17671,17720,17922,18163,18200,18273,18287,18409,18415,18521,18536,18616,18726,18808,18845,19105,19185,19202,19406,19437,19581,19612,19640,19655,19876,19945,19971,20116,20202,20381,20416,20433,20593,20669,20770,20868,20984,21057,21139,21324,21356,21427,21567,21609,21625,21658,21925,21950,22039,22105,22228,22375,22400,22531,22604,22649,22698,22869,22987,23026,23059,23147,23159,23206,23279,23383,23521,23723,23872,24143,24241,24266,24289,24565,24586,24768,24786,24932,25323,25341,25368,25379,25502,25517,25566,25569,25570,25598,25633,25669,25688,25693,25732,25743,25755,25788,25810,25854,25957,25970,25990,26037,26106,26205,26226,26240,26249,26334,26368,26460,26474,26483,26486,26490,26615,26657,26682,26683,26695,26729,26741,26780,26786,26834,26900,26942,26958,26974,27015,27041,27069,27124,27137,27174,27175,27229,27302,27304,27348,27351,27362,27381,27441,27465,27466,27488,27521,27525,27545,27550,27552,27564,27581,27585,27586,27591,27592,27660,27720,27728,27736,27737,27819,27840,27843,27855,27856,27902,27914,27949,27965,27987,28014,28072,28097,28170,28192,28225,28235,28248,28258,28359,28486,28537,28541,28563,28571,28622,28658,28671,28698,28713,28725,28752,28768,28769,28776,28832,28840,28844,28861,28888,28954,29034,29044,29058,29061,29069,29072,29075,29100,29143,29181,29188,29217,29228,29272,29275,29289,29290,29295,29367,29387,29424,29427,29585,29622,29641,29716,29735,29760,29785,29844,29854,29886,29892,30057,30067,30132,30135,30174,30195,30197,30198,30271,30290,30295,30297,30308,30334,30345,30356,30367,30372,30430,30494,30503,30508,30509,30531,30539,30557,30591,30601,30633,30637,30706,30714,30720,30769,30800,30835,30870,30902,30915,30995,31003,31004,31037,31055,31117,31144,31148,31189,31210,31218,31257,31279,31329,31343,31390,31400,31427,31440,31446,31460,31483,31509,31557,31581,31605,31608,31622,31709,31729,31746,31749,31838,31840,31849,31959,32020,32121,32141,32142,32171,32192,32217,32227,32247,32262,32276,32283,32327,32438,32519,32585,32627,32643,32713,32817,32853,32857,32905,32924,32937,32970,32995,33048,33049,33085,33117,33118,33151,33190,33198,33208,33209,33227,33228,33237,33240,33250,33323,33438,33508,33558,33688,33729,33750,33864,33897,34017,34031,34042,34043,34052,34058,34088,34117,34121,34123,34159,34169,34209,34339,34409,34464,34600,34616,34617,34635,34653,34664,34731,34774,34944,35018,35054,35064,35065,35166,35174,35186,35212,35215,35285,35328,35370,35436,35475,35663,35667,35849,35870,35879,35934,35953,35975,36016,36049,36063,36070,36091,36092,36127,36136,36229,36332,36334,36343,36411,36423,36431,36436,36622,36625,36648,36722,36774,36784,36834,36848,36890,36918,36948,37025,37057,37162,37163,37193,37220,37299,37339,37361,37393,37446,37485,37494,37545,37547,37578,37597,37637,37652,37660,37674,37742,37792,37873,37911,37926,37952,38105,38130,38202,38213,38229,38256,38318,38335,38402,38435,38436,38529,38566,38578,38626,38639,38671,38740,38913,38925,38950,38981,38983,39005,39014,39019,39158,39183,39267,39271,39275,39281,39343,39350,39357,39365,39397,39425,39449,39500,39505,39525,39567,39598,39607,39624,39626,39637,39646,39665,39677,39787,39833,39872,39878,39883,39912,39958,39966,39986,40001,40007,40037,40064,40072,40097,40137,40178,40218,40290,40299,40354,40380,40399,40455,40461,40475,40509,40510,40514,40527,40562,40570,40592,40665,40679,40697,40730,40757,40822,40829,40954,40956,40999,41005,41014,41016,41021,41024,41045,41095,41096,41111,41158,41166,41177,41194,41197,41200,41299,41312,41341,41354,41366,41396,41401,41402,41408,41410,41413,41427,41430,41452,41508,41536,41602,41643,41648,41653,41670,41675,41678,41725,41799,41820,41834,41870,41882,41900,41928,41935,41940,41997,42014,42048,42073,42074,42080,42082,42090,42175,42223,42297,42317,42321,42330,42351,42355,42368,42395,42412,42426,42436,42446,42447,42468,42471,42495,42507,42612,42627,42630,42655,42672,42714,42716,42730,42797,42848,42905,42909,42932,42948,43088,43141,43181,43204,43211,43218,43231,43246,43262,43267,43272,43326,43332,43375,43429,43433,43441,43488,43534,43588,43589,43614,43616,43623,43646,43677,43734,43779,43812,43850,43890,43893,43920,43927,43934,43940,43984,44004,44010,44036,44043,44048,44089,44104,44118,44157,44192,44248,44249,44258,44261,44318,44320,44327,44328,44351,44362,44364,44371,44380,44459,44494,44499,44512,44513,44521,44538,44569,44572,44623,44651,44703,44732,44740,44741,44820,44838,44853,44894,44951,44966,44969,45048,45053,45110,45153,45159,45166,45213,45225,45231,45232,45283,45299,45327,45329,45345,45373,45374,45422,45435,45563,45912,46893,46943,46961,47772,48353,48391,48860,49005,49218,49306,49339,49437,49502,49534,49627,49716,49723,49759,49771,49796,49813,49882,49895,49910,49978,49987,50065,50067,50163,50350,50399,50434,50446,50486,50572,50651,50659,50796,50821,50899,50907,50914,51208,51454,51584,51660,51669,51744,51823,51884,52021,52258,52320,52330,52390,52391,52392,52621,52629,52692,52748,52753,52764,52801,52865,52940,52989,53048,53083,53163,53167,53301,53308,53315,53320,53528,53751,53796,53817,53876,54036,54122,54130,54400,54446,54613,54670,54721,54884,54993,54997,55024,55132,55237,55277,55300,55587,55668,55730,56054,56223,56322,56406,56421,56461,56466,56589,56696,56804,56911,56995,57036,57085,57161,57311,57337,57667,57747,57845,58160,58235,58309,58317,58341,58370,58400,58455,58515,58691,58710,58850,59212,59259,59272,59292,59400,59415,59492,59559,59593,59702,59714,59899,59929,60115,60146,60183,60262,60270,60287,60574,60591,60593,60612,60633,60657,60956,60968,60975,60977,60995,61049,61161,61223,61573,61673,61696,61750,61753,61759,61797,61910,61983,62018,62154,62174,62224,62251,62327,62429,62548,62648,62730,62768,62854,63020,63061,63142,63205,63223,63251,63370,63486,63580,63607,63660,63673,63711,63712,63746,63837,63859,63886,63893,63943,63972,63996,64003,64046,64062,64073,64080,64207,64266,64285,64399,64430,64489,64511,64515,64587,64590,64629,64641,64691,64727,64736,64873,64966,64992,65061,65068,65093,65134,65167,65750,65915,65953,66116,66231,66252,66254,66386,66440,66535,66728,66821,66965,67001,67135,67291,67571,67624,67634,67647,67743,67806,68054,68169,68223,68224,68396,68417,68463,68471,68797,68882,68948,69011,69134,69178,69207,69343,69420,69578,69579,69678,69794,69987,77005);");
	$stmt->execute();

	$i = $stmt->rowCount();
	while($contrato = $stmt->fetch(PDO::FETCH_ASSOC)) {
		// Correções de verificações da API do IXC (buga se enviar os valores como 0):
		if($contrato['avalista_1'] == 0 && $contrato['avalista_2'] == 0) {
			$contrato['avalista_1'] = '';
			$contrato['avalista_2'] = '';
		}
		if ($contrato['motivo_cancelamento'] == 0)
			$contrato['motivo_cancelamento'] = ' ';

		// ----------------- Regras Elias: -------------------

		if($contrato['id_filial'] == 7)
			$contrato['id_filial'] = 2;

		if ($contrato['id_carteira_cobranca'] == 4) {
			$contrato['id_carteira_cobranca'] = 1;
			$contrato['tipo_cobranca'] = 'P';
		}
		else if ($contrato['id_carteira_cobranca'] == 11) {
			$contrato['id_carteira_cobranca'] = 6;
			$contrato['tipo_cobranca'] = 'E';
		}

		if($contrato['tipo_doc_opc'] == 11)
			$contrato['tipo_doc_opc'] = 10;

		if($contrato['tipo_doc_opc2'] == 114)
			$contrato['tipo_doc_opc2'] = 104;

		if($contrato['tipo_doc_opc3'] == 113)
			$contrato['tipo_doc_opc3'] = 128;

		$resposta = updateRow($contrato, 'cliente_contrato');

		// var_dump($resposta);
		echo --$i . PHP_EOL;
	}
}

main();