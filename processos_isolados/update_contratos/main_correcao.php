<?php

require_once __DIR__ . '/../../common/config.php';

function main()
{
	$dbh_ixc = getConnectionIxc();
	$dbh_noc = getConnectionNoc();

	$stmt = $dbh_noc->prepare("SELECT id,
	data,
	data_validade,
	pago_ate_data,
	nao_bloquear_ate,
	nao_avisar_ate,
	data_renovacao,
	data_ativacao,
	dt_ult_ativacao,
	dt_ult_inativacao,
	dt_utl_negativacao,
	dt_ult_desb_conf,
	dt_ult_desativacao,
	dt_ult_bloq_manual,
	dt_ult_bloq_auto,
	dt_ult_finan_atraso,
	dt_ult_desiste,
	dt_ult_des_bloq_conf,
	data_acesso_desativado,
	dt_ult_liberacao_susp_parc,
	nao_susp_parc_ate,
	data_inicial_suspensao,
	data_final_suspensao,
	data_retomada_contrato,
	dt_ult_liberacao_temporaria,
	data_desistencia FROM temp.clientes_que_terao_filial_alterada_dados_antes_da_alteracao");
	$stmt->execute();

	$datas_antigas = [];
	while($contrato = $stmt->fetch(PDO::FETCH_ASSOC)) {
		$datas_antigas[$contrato['id']] = $contrato;
	}

	$stmt = $dbh_ixc->prepare("SELECT *
	FROM cliente_contrato
	WHERE id IN (98,661,3607,5923,11882,14666,15199,16693,16744,17403,19422,21943,25446,26003,31701,32138,32489,32530,32606,32841,33172,33285,33343,33428,33567,33583,33599,33921,34120,34127,34238,34301,34302,34733,34838,34844,34913,35165,35277,35350,35415,35719,35781,35814,35829,36047,36071,36119,36124,36183,36518,36579,36747,36778,36808,36861,36963,37022,37126,37208,37438,37541,37649,37709,37853,37904,37942,38082,38186,38242,38346,38459,38500,38517,38571,38705,38718,38738,38862,38916,38931,38964,38974,38985,39236,39333,39337,39380,39670,39676,39699,39885,39927,39931,40032,40068,40252,40389,40446,40502,40512,40531,40575,40621,40622,40700,40753,40833,40951,40967,40979,40987,41135,41192,41204,41288,41323,41346,41347,41360,41393,41404,41468,41475,41490,41498,41583,41624,41625,41627,41631,41711,41722,41732,41752,41755,41769,41801,41877,41934,41985,42003,42092,42152,42159,42231,42244,42280,42286,42382,42599,42633,42725,42798,42823,42825,42963,42964,42975,42976,42988,43067,43077,43103,43164,43225,43279,43284,43318,43359,43372,43374,43409,43425,43484,43520,43521,43536,43558,43657,43680,43705,43756,43758,43771,43811,43827,43854,43973,43988,44005,44014,44038,44092,44177,44186,44189,44282,44302,44305,44322,44337,44486,44495,44519,44560,44561,44573,44579,44580,44666,44689,44693,44748,44816,44837,44846,44849,44852,44865,44871,44887,44921,44936,44961,45019,45080,45083,45094,45107,45145,45160,45205,45221,45226,45229,45263,45371,45398,45459,45468,45497,45500,45508,45519,45526,45538,45539,45540,45552,45575,45579,45582,45587,45590,45591,45614,45617,45625,45648,45659,45668,45680,45687,45690,45692,45695,45711,45722,45731,45740,45753,45758,45760,45767,45770,45798,45812,45816,45820,45832,45842,45843,45844,45851,45858,45862,45890,45892,45898,45903,45905,45911,45920,45926,45927,45936,45939,45944,45959,45965,45968,45972,45976,45995,46008,46011,46024,46026,46030,46033,46037,46041,46044,46047,46048,46049,46052,46058,46086,46089,46096,46097,46106,46108,46110,46114,46127,46141,46150,46151,46154,46157,46168,46171,46178,46181,46186,46191,46202,46207,46212,46215,46219,46222,46228,46242,46246,46249,46268,46270,46274,46280,46296,46306,46308,46322,46329,46331,46336,46338,46347,46354,46356,46357,46359,46366,46368,46369,46374,46377,46378,46399,46403,46413,46418,46433,46438,46442,46443,46452,46465,46479,46487,46488,46489,46493,46503,46518,46521,46533,46538,46544,46551,46557,46562,46563,46564,46571,46579,46585,46586,46593,46598,46599,46609,46613,46616,46620,46629,46630,46641,46646,46656,46662,46671,46681,46700,46702,46703,46713,46719,46725,46726,46731,46738,46740,46756,46777,46783,46790,46793,46802,46813,46816,46821,46822,46827,46830,46839,46842,46843,46851,46861,46865,46868,46871,46923,46930,46938,46944,46948,46952,46953,46954,46956,46975,46982,46987,46995,46997,47000,47009,47016,47021,47028,47030,47039,47040,47054,47056,47066,47084,47087,47091,47098,47103,47122,47123,47134,47136,47152,47154,47157,47169,47186,47188,47189,47190,47191,47194,47197,47199,47201,47205,47216,47221,47222,47228,47235,47253,47259,47265,47269,47272,47273,47279,47290,47294,47306,47310,47332,47335,47341,47343,47347,47348,47355,47358,47359,47371,47385,47411,47412,47413,47420,47444,47445,47459,47460,47463,47464,47468,47470,47471,47479,47480,47491,47517,47520,47530,47535,47545,47547,47549,47550,47563,47578,47599,47606,47607,47608,47614,47617,47625,47632,47634,47639,47646,47648,47649,47652,47653,47670,47677,47678,47679,47681,47684,47699,47707,47708,47710,47716,47721,47723,47724,47725,47730,47738,47741,47746,47751,47752,47763,47764,47770,47780,47783,47791,47795,47798,47799,47801,47803,47806,47810,47815,47824,47825,47830,47831,47854,47857,47858,47865,47869,47877,47884,47888,47911,47912,47919,47930,47934,47936,47937,47947,47965,47967,47968,47970,47975,47986,47989,47993,48016,48024,48030,48036,48047,48058,48062,48067,48068,48071,48079,48082,48088,48100,48106,48108,48111,48117,48118,48125,48132,48136,48138,48153,48157,48160,48162,48168,48169,48190,48192,48207,48210,48217,48223,48237,48241,48242,48243,48260,48264,48279,48281,48289,48300,48302,48307,48311,48315,48319,48323,48336,48342,48354,48356,48363,48382,48387,48394,48395,48399,48400,48408,48421,48424,48427,48432,48438,48446,48447,48449,48450,48466,48483,48495,48500,48502,48508,48531,48538,48539,48540,48542,48548,48561,48566,48567,48573,48576,48589,48594,48603,48609,48610,48612,48613,48623,48627,48632,48637,48639,48640,48647,48668,48672,48679,48682,48692,48694,48701,48703,48712,48758,48760,48764,48766,48773,48780,48789,48793,48806,48807,48813,48821,48826,48827,48830,48831,48833,48837,48845,48853,48865,48868,48894,48896,48900,48906,48907,48914,48920,48935,48938,48939,48956,48959,48964,48971,48982,48985,48986,48989,48998,49006,49020,49032,49034,49035,49045,49048,49069,49070,49073,49075,49076,49080,49090,49091,49102,49123,49130,49139,49147,49152,49153,49159,49160,49165,49183,49184,49193,49194,49200,49206,49217,49234,49256,49257,49258,49261,49271,49286,49287,49301,49302,49319,49322,49342,49362,49371,49372,49377,49385,49397,49412,49418,49426,49432,49439,49449,49476,49480,49494,49497,49505,49517,49522,49529,49532,49538,49540,49545,49548,49561,49584,49595,49598,49604,49623,49626,49631,49633,49644,49650,49662,49663,49673,49674,49763,50023,50540,50746,50748,51059,52016,52229,52507,53588,53593,53596,53610,53611,53615,53622,53638,53651,53661,53665,53667,53670,53683,53688,53706,53717,53724,53727,53729,53734,53736,53737,53740,53743,53752,53754,53759,53774,53775,53777,53786,53788,53800,53809,53813,53816,53825,53828,53832,53834,53850,53854,53855,53865,53877,53881,53883,53885,53887,53890,53895,53909,53934,53943,53946,53947,53948,53963,53964,53965,53966,53984,53993,54000,54002,54005,54008,54022,54029,54034,54041,54044,54045,54060,54074,54077,54082,54083,54089,54092,54093,54094,54097,54100,54103,54104,54117,54120,54121,54133,54136,54139,54145,54148,54156,54157,54172,54175,54179,54187,54190,54225,54231,54234,54239,54240,54243,54245,54251,54252,54253,54254,54256,54258,54262,54292,54294,54298,54300,54316,54319,54329,54331,54335,54338,54351,54353,54354,54356,54357,54359,54363,54368,54371,54374,54382,54383,54386,54388,54391,54393,54394,54395,54418,54421,54460,54464,54469,54474,54495,54497,54500,54501,54511,54515,54517,54530,54533,54537,54549,54554,54559,54561,54562,54575,54593,54594,54596,54600,54605,54609,54624,54638,54645,54647,54655,54662,54674,54679,54687,54714,54715,54725,54740,54741,54763,54764,54765,54789,54794,54804,54806,54823,54843,54845,54848,54853,54859,54863,54865,54886,54890,54923,54924,54926,54935,54936,55124,55125,55149,55224,55330,55335,55337,55364,55366,55619,56344,57869,58236,58576,59250,59361,59368,59747,59874,59885,60042,60098,60610,61079,61104,61170,61186,61387,61388,61399,61421,61422,61437,61488,61492,61503,61515,61524,61533,61536,61541,61545,61548,61552,61577,61587,61588,61592,61609,61616,61617,61623,61629,61638,61643,61655,61663,61672,61676,61697,61698,61704,61706,61707,61710,61711,61714,61723,61724,61726,61731,61733,61735,61742,61744,61747,61757,61766,61772,61778,61782,61796,61799,61806,61823,61824,61855,61862,61873,61877,61897,61900,61908,61922,61923,61927,61929,61935,61937,61942,61944,61946,61947,61949,61952,61955,61957,61959,61974,61977,61978,61980,61984,61987,61991,61998,62000,62003,62006,62039,62044,62046,62049,62052,62053,62055,62066,62068,62069,62071,62077,62079,62082,62086,62091,62094,62096,62097,62108,62117,62126,62127,62129,62132,62133,62134,62136,62139,62142,62148,62151,62155,62158,62161,62173,62175,62191,62200,62209,62212,62213,62215,62218,62219,62220,62223,62226,62227,62231,62234,62240,62249,62252,62256,62263,62266,62274,62275,62280,62287,62289,62295,62296,62300,62313,62314,62317,62319,62321,62325,62337,62344,62348,62349,62374,62375,62390,62397,62404,62413,62414,62419,62437,62440,62455,62464,62468,62480,62487,62506,62509,62511,62520,62524,62525,62527,62528,62543,62551,62554,62555,62568,62570,62572,62573,62576,62579,62591,62603,62607,62624,62628,62629,62638,62644,62649,62650,62651,62656,62663,62674,62678,62683,62696,62714,62719,62720,62721,62728,62731,62739,62743,62744,62763,62767,62769,62770,62782,62786,62792,62797,62801,62817,62824,62825,62826,62830,62848,62851,62859,62860,62877,62888,62896,62911,62918,62919,62923,62935,62940,62943,62966,62975,62984,62991,63000,63002,63017,63018,63027,63029,63031,63034,63036,63038,63043,63046,63048,63060,63065,63066,63078,63084,63098,63101,63103,63106,63108,63110,63113,63129,63131,63132,63143,63144,63150,63156,63177,63184,63190,63191,63192,63198,63203,63204,63213,63214,63216,63224,63225,63229,63234,63235,63241,63242,63243,63257,63288,63291,63294,63306,63309,63312,63313,63314,63316,63334,63338,63340,63342,63345,63364,63368,63371,63372,63379,63380,63388,63389,63396,63408,63411,63412,63415,63419,63432,63433,63436,63437,63439,63450,63452,63456,63457,63461,63463,63466,63477,63478,63490,63493,63495,63497,63529,63536,63539,63540,63544,63545,63548,63550,63551,63552,63556,63558,63574,63598,63606,63609,63622,63623,63632,63640,63645,63647,63651,63654,63656,63658,63663,63664,63672,63687,63689,63693,63697,63707,63709,63713,63715,63717,63719,63724,63727,63732,63740,63742,63750,63751,63757,63762,63763,63772,63776,63781,63792,63793,63795,63800,63804,63815,63823,63827,63834,63844,63852,63856,63858,63861,63873,63880,63882,63885,63888,63889,63894,63896,63902,63913,63914,63929,63936,63945,63948,63957,63960,63962,63965,63971,63975,63977,63995,64004,64010,64017,64031,64036,64040,64042,64051,64054,64059,64070,64085,64094,64108,64124,64126,64133,64136,64152,64153,64157,64166,64173,64179,64183,64185,64194,64202,64212,64214,64217,64220,64225,64228,64229,64231,64247,64257,64258,64268,64269,64270,64275,64287,64294,64297,64307,64313,64321,64327,64335,64340,64348,64351,64352,64355,64368,64391,64395,64409,64415,64420,64421,64424,64426,64436,64457,64469,64482,64488,64510,64519,64524,64530,64534,64550,64555,64559,64569,64574,64575,64576,64579,64581,64583,64608,64615,64626,64628,64640,64644,64648,64650,64661,64670,64676,64677,64679,64683,64684,64695,64698,64709,64711,64718,64721,64729,64732,64734,64735,64752,64838,65556,65782,65822,65853,67230);");
	$stmt->execute();

	$i = $stmt->rowCount();
	while($contrato = $stmt->fetch(PDO::FETCH_ASSOC)) {
		// Correções de verificações da API do IXC (buga se enviar os valores como 0):
		if($contrato['avalista_1'] == 0 && $contrato['avalista_2'] == 0) {
			$contrato['avalista_1'] = '';
			$contrato['avalista_2'] = '';
		}
		if ($contrato['motivo_cancelamento'] == 0)
			$contrato['motivo_cancelamento'] = ' ';

		// ----------------- CORREÇÃO: -------------------
		
		$contrato['data'] = $datas_antigas[$contrato['id']]['data'];
		$contrato['data_validade'] = $datas_antigas[$contrato['id']]['data_validade'];
		$contrato['pago_ate_data'] = $datas_antigas[$contrato['id']]['pago_ate_data'];
		$contrato['nao_bloquear_ate'] = $datas_antigas[$contrato['id']]['nao_bloquear_ate'];
		$contrato['nao_avisar_ate'] = $datas_antigas[$contrato['id']]['nao_avisar_ate'];
		$contrato['data_renovacao'] = $datas_antigas[$contrato['id']]['data_renovacao'];
		$contrato['data_ativacao'] = $datas_antigas[$contrato['id']]['data_ativacao'];
		$contrato['dt_ult_ativacao'] = $datas_antigas[$contrato['id']]['dt_ult_ativacao'];
		$contrato['dt_ult_inativacao'] = $datas_antigas[$contrato['id']]['dt_ult_inativacao'];
		$contrato['dt_utl_negativacao'] = $datas_antigas[$contrato['id']]['dt_utl_negativacao'];
		$contrato['dt_ult_desb_conf'] = $datas_antigas[$contrato['id']]['dt_ult_desb_conf'];
		$contrato['dt_ult_desativacao'] = $datas_antigas[$contrato['id']]['dt_ult_desativacao'];
		$contrato['dt_ult_bloq_manual'] = $datas_antigas[$contrato['id']]['dt_ult_bloq_manual'];
		$contrato['dt_ult_bloq_auto'] = $datas_antigas[$contrato['id']]['dt_ult_bloq_auto'];
		$contrato['dt_ult_finan_atraso'] = $datas_antigas[$contrato['id']]['dt_ult_finan_atraso'];
		$contrato['dt_ult_desiste'] = $datas_antigas[$contrato['id']]['dt_ult_desiste'];
		$contrato['dt_ult_des_bloq_conf'] = $datas_antigas[$contrato['id']]['dt_ult_des_bloq_conf'];
		$contrato['data_acesso_desativado'] = $datas_antigas[$contrato['id']]['data_acesso_desativado'];
		$contrato['dt_ult_liberacao_susp_parc'] = $datas_antigas[$contrato['id']]['dt_ult_liberacao_susp_parc'];
		$contrato['nao_susp_parc_ate'] = $datas_antigas[$contrato['id']]['nao_susp_parc_ate'];
		$contrato['data_inicial_suspensao'] = $datas_antigas[$contrato['id']]['data_inicial_suspensao'];
		$contrato['data_final_suspensao'] = $datas_antigas[$contrato['id']]['data_final_suspensao'];
		$contrato['data_retomada_contrato'] = $datas_antigas[$contrato['id']]['data_retomada_contrato'];
		$contrato['dt_ult_liberacao_temporaria'] = $datas_antigas[$contrato['id']]['dt_ult_liberacao_temporaria'];
		$contrato['data_desistencia'] = $datas_antigas[$contrato['id']]['data_desistencia'];

		$resposta = updateRow($contrato, 'cliente_contrato');

		//var_dump($resposta);

		echo --$i . PHP_EOL;
	}
}

main();