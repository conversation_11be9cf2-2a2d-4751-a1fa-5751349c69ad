<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/Logger.php';

$f = fopen(__DIR__ . '/lock', 'w') or die('Cannot create lock file');
if (flock($f, LOCK_EX | LOCK_NB)) {
	main();
}

function main() {
	$dbh_radius = getConnectionRadius();
	$dbh_ixc = getConnectionIxc();

	$sql = "select 
	ixc_radusuarios.login,
	ixc_radusuarios.pd_ipv6,
	radipv6_allocated.allocated_prefix
from 
	fdwtables.ixc_radusuarios
inner join 
	radipv6_allocated 
		on ixc_radusuarios.login = radipv6_allocated.username
where 
	ixc_radusuarios.pd_ipv6 != radipv6_allocated.allocated_prefix::text
	and radipv6_allocated.attribute = 'Delegated-Ipv6-Prefix'
	and ixc_radusuarios.pd_ipv6 != '';";
	$stmt = $dbh_radius->prepare($sql);
	$stmt->execute();

	$duplicates = $stmt->fetchAll(PDO::FETCH_ASSOC);
	
	foreach($duplicates as $duplicate) {
		$stmt = $dbh_ixc->prepare("SELECT * FROM radusuarios ru WHERE ru.login = ?;");
		$stmt->execute(array($duplicate['login']));

		$radusuario = $stmt->fetch(PDO::FETCH_ASSOC);

		$radusuario['pd_ipv6'] = '';
		$radusuario['framed_pd_ipv6'] = '';

		$radusuario['fixar_ip'] = 'N';
		$radusuario['fixar_ipv6'] = 'N';
		$radusuario['framed_fixar_ipv6'] = 'N';

		var_dump(updateRadusuario($radusuario));
	}
}