<?php

require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/utils.php';
require_once __DIR__ . '/../../common/Logger.php';

$api = getIxcApi();

main();

function main() {
    $dbh_noc = getConnectionNoc();
    $dbh_ixc = getConnectionIxc();

    $stmt = $dbh_ixc->prepare("SELECT 
            cc.id AS id_contrato,
            cl.razao AS cliente,
            (CASE
                WHEN ci1.nome IS NOT NULL THEN ci1.nome
                WHEN ci2.nome IS NOT NULL THEN ci2.nome
                ELSE NULL
            END) AS cidade,
            cc.data_cancelamento,
            cc.motivo_cancelamento AS id_motivo,
            mc.motivo AS motivo_cancelamento,
            v.nome AS vendedor,
            vc.valor
        FROM cliente_contrato cc
        INNER JOIN cliente cl ON cl.id = cc.id_cliente
        LEFT JOIN vd_contratos vc ON vc.id = cc.id_vd_contrato
        LEFT JOIN (
            SELECT cc.id AS id_contrato,
                SUM((
                (CASE WHEN vcp1.valor_unit IS NOT NULL THEN vcp1.valor_unit ELSE vcp2.valor_unit END)
                + (CASE WHEN cca1.valor > 0 THEN cca1.valor
                WHEN cca2.valor > 0 THEN cca2.valor ELSE 0 END)
                - (CASE WHEN ccd1.valor > 0 THEN ccd1.valor
                WHEN ccd2.valor > 0 THEN ccd2.valor ELSE 0 END)
                )) AS valor
            FROM cliente_contrato cc
            LEFT JOIN vd_contratos_produtos vcp1 ON vcp1.id_vd_contrato = cc.id_vd_contrato
            LEFT JOIN vd_contratos_produtos vcp2 ON vcp2.id_contrato = cc.id
            LEFT JOIN cliente_contrato_descontos ccd1 ON ccd1.id_vd_contrato_produtos = vcp1.id AND ccd1.id_contrato = cc.id AND (ccd1.data_validade = '0000-00-00' OR ccd1.data_validade IS NULL OR ccd1.data_validade > NOW())
            LEFT JOIN cliente_contrato_descontos ccd2 ON ccd2.id_vd_contrato_produtos = vcp2.id AND ccd2.id_contrato = cc.id AND (ccd2.data_validade = '0000-00-00' OR ccd2.data_validade IS NULL OR ccd2.data_validade > NOW())
            LEFT JOIN cliente_contrato_acrescimos cca1 ON cca1.id_vd_contrato_produtos = vcp1.id AND cca1.id_contrato = cc.id AND (cca1.data_validade = '0000-00-00' OR cca1.data_validade IS NULL OR cca1.data_validade > NOW())
            LEFT JOIN cliente_contrato_acrescimos cca2 ON cca2.id_vd_contrato_produtos = vcp2.id AND cca2.id_contrato = cc.id AND (cca2.data_validade = '0000-00-00' OR cca2.data_validade IS NULL OR cca2.data_validade > NOW())
            GROUP BY id_contrato
        ) vc ON vc.id_contrato = cc.id
        LEFT JOIN fn_areceber_mot_cancelamento mc ON mc.id = cc.motivo_cancelamento
        LEFT JOIN cidade ci1 ON ci1.id = cc.cidade
        LEFT JOIN cidade ci2 ON ci2.id = cl.cidade
        LEFT JOIN vendedor v ON v.id = cc.id_vendedor
        WHERE cc.data_cancelamento BETWEEN '2023-01-01' AND '2023-06-07'
            AND motivo_cancelamento = 745;");
    $stmt->execute();

    $contratos = $stmt->fetchAll(PDO::FETCH_ASSOC);

    foreach($contratos as $contrato) {
        $id_contrato = $contrato['id_contrato'];
        
        $contrato['atendente'] = getLogCancelamentoOperador($id_contrato);

        try {
            $stmt = $dbh_noc->prepare("INSERT INTO temp.contratos_cancelados_retro (id_contrato, cliente, cidade, data_cancelamento, id_motivo, motivo_cancelamento, valor, atendente, data_coleta, vendedor) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?);");
            $stmt->execute([
                $contrato['id_contrato'],
                $contrato['cliente'],
                $contrato['cidade'],
                $contrato['data_cancelamento'],
                $contrato['id_motivo'],
                $contrato['motivo_cancelamento'],
                $contrato['valor'],
                $contrato['atendente'],
                date('Y-m-d H:i:s'),
                $contrato['vendedor']
            ]);
        }
        catch(Exception $e) {
            echo "$id_contrato, ";
            echo $e->getMessage();
        }
    }
}

function getLogCancelamentoOperador($id_contrato) {
    global $api;

    $params = array(
        'qtype' => 'ixc_logs.id',
        'query' => '',
        'oper' => '!=',
        'sortname' => 'ixc_logs.id',
        'sortorder' => 'desc',
        'page' => '1',
        'rp' => '90000',
        'grid_param' => json_encode([
            [
                'TB' => 'ixc_logs.tabela',
                'OP'=>'=',
                'P'=>'cliente_contrato'
            ],
            [
                'TB' => 'ixc_logs.id_tabela',
                'OP'=>'=',
                'P'=>$id_contrato
            ]
    ]));
    $api->get('ixc_logs', $params);
    $retorno = $api->getRespostaConteudo(true); // false para json | true para array

    $operador = null;
    
    foreach ($retorno['registros'] as $registro) {
        $campos = json_decode($registro['campos'], true);
    
        if (!isset($campos['data_cancelamento'])
            || !$campos['data_cancelamento']
            || $campos['data_cancelamento'] === ''
            || $campos['data_cancelamento'] === '0000-00-00')
            continue;

        $operador = utf8_encode($registro['operador']);
        break;
    }

    return $operador;
}