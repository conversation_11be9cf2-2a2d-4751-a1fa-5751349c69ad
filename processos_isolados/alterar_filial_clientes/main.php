<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/utils.php';

function main() {
	$dbh = getConnectionIxc();

	$stmt = $dbh->prepare("SELECT DISTINCT cl.*
        FROM cliente cl
        INNER JOIN cliente_contrato cc ON cc.id_cliente = cl.id
            AND cc.status NOT IN ('D', 'I')
        WHERE cl.filial_id != 2
            AND cl.ativo = 'S'");
            $stmt->execute();

	$clientes = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $i = 1;
	foreach($clientes as $cliente) {
        echo $i++ . ' - ' . $cliente['id'];
        echo PHP_EOL;

		$cliente['filial_id'] = '2';
        // $resposta = ['type' => '', 'message' => ''];
		$resposta = updateCliente($cliente);

        if ($resposta['type'] === 'success' && $resposta['message'] === 'Registro atualizado com sucesso!')
            echo 'SUCCESS';
        else
            var_dump($resposta);

        echo PHP_EOL;
        echo PHP_EOL;
	}
}

main();
