<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/Logger.php';

$f = fopen(__DIR__ . '/lock', 'w') or die('Cannot create lock file');
if (flock($f, LOCK_EX | LOCK_NB)) {
	main();
}

function main() {
    $dbh = getConnectionIxc();
    $stmt = $dbh->prepare("SELECT * FROM radacct WHERE radacctid = 2520988;");
    $stmt->execute();

    $row = $stmt->fetch(PDO::FETCH_ASSOC);

    $row['acctstoptime'] = '2023-06-05 15:32:42.0';
    // var_dump($row);
    // exit;


    $api = getIxcApi();
	$api->put('radacct', $row, 2520988);
	$resposta = $api->getRespostaConteudo(true); // false para json | true para array

    var_dump($resposta);
}