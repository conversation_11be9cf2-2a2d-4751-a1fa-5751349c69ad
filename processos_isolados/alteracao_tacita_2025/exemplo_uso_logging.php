<?php
/**
 * Exemplo de uso das funções de logging consolidadas
 * Este arquivo demonstra como usar as novas funções de logging
 */

require_once __DIR__ . '/helpers.php';

/**
 * Exemplo de função que atualiza um contrato com logging detalhado
 */
function exemploAtualizarContrato($id_contrato, $novo_plano_venda) {
    $tempo_inicio = microtime(true);
    
    // Log de início da função
    logInicioFuncao($id_contrato, 'atualizarContrato', [
        'id_contrato' => $id_contrato,
        'novo_plano_venda' => $novo_plano_venda
    ]);

    try {
        // Busca dados antes da alteração
        $dados_antes = getDadosContrato($id_contrato);
        
        // Simula atualização do contrato
        global $dbh_ixc;
        $stmt = $dbh_ixc->prepare("UPDATE cliente_contrato SET id_vd_contrato = ? WHERE id = ?");
        $resultado = $stmt->execute([$novo_plano_venda, $id_contrato]);

        if ($resultado) {
            // Busca dados depois da alteração
            $dados_depois = getDadosContrato($id_contrato);
            
            // Log da alteração de dados
            logAlteracaoDados(
                $id_contrato, 
                'UPDATE_CONTRATO', 
                'cliente_contrato', 
                $dados_antes, 
                $dados_depois, 
                "id = {$id_contrato}"
            );

            // Log de sucesso
            logFimFuncao($id_contrato, 'atualizarContrato', true, [
                'plano_anterior' => $dados_antes['plano_venda'],
                'plano_novo' => $dados_depois['plano_venda']
            ], $tempo_inicio);

            return true;
        } else {
            // Log de erro
            logFimFuncao($id_contrato, 'atualizarContrato', false, [
                'erro' => 'Falha na execução da query UPDATE'
            ], $tempo_inicio);

            return false;
        }

    } catch (Exception $e) {
        // Log de exceção
        logAlteracao($id_contrato, 'EXCEPTION_atualizarContrato', 'Exceção durante atualização do contrato', [
            'exception_message' => $e->getMessage(),
            'exception_code' => $e->getCode(),
            'parametros' => [
                'id_contrato' => $id_contrato,
                'novo_plano_venda' => $novo_plano_venda
            ]
        ]);

        logFimFuncao($id_contrato, 'atualizarContrato', false, [
            'erro' => $e->getMessage()
        ], $tempo_inicio);

        return false;
    }
}

/**
 * Exemplo de processamento completo de um contrato
 */
function exemploProcessarContrato($id_contrato) {
    echo "Processando contrato: {$id_contrato}\n";

    // Verifica se já foi processado
    if (contratoJaProcessado($id_contrato)) {
        echo "Contrato já foi processado anteriormente.\n";
        $relatorio = getRelatorioContrato($id_contrato);
        echo "Status atual: " . $relatorio['resultado']['status'] . "\n";
        return;
    }

    $passos_executados = [];
    $dados_antes = getDadosContrato($id_contrato);

    // Simula execução de vários passos
    $passos = [
        'verificarContrato' => function($id) { return true; }, // Simula sucesso
        'atualizarContrato' => function($id) { return exemploAtualizarContrato($id, 123); },
        'atualizarPlanoVelocidade' => function($id) { return true; },
        'restaurarFaturasAbertas' => function($id) { return true; },
        'inserirDescontosAcrescimos' => function($id) { return false; } // Simula falha
    ];

    $sucesso_geral = true;

    foreach ($passos as $nome_passo => $funcao) {
        echo "Executando: {$nome_passo}...\n";
        
        if ($funcao($id_contrato)) {
            $passos_executados[] = $nome_passo;
            echo "✓ {$nome_passo} executado com sucesso\n";
        } else {
            $sucesso_geral = false;
            echo "✗ Falha em: {$nome_passo}\n";
            break; // Para no primeiro erro
        }
    }

    // Busca dados finais
    $dados_depois = getDadosContrato($id_contrato);

    // Define o resultado final
    if ($sucesso_geral) {
        setExecuted($id_contrato, $passos_executados, [
            'processamento_completo' => true,
            'total_passos' => count($passos_executados)
        ], $dados_antes, $dados_depois);
        echo "✓ Contrato processado com SUCESSO!\n";
    } else {
        setFailed($id_contrato, $passos_executados, [
            'processamento_completo' => false,
            'passos_executados' => count($passos_executados),
            'total_passos' => count($passos)
        ], $dados_antes, $dados_depois);
        echo "✗ Contrato processado com FALHAS!\n";
    }

    // Exibe relatório
    echo "\n--- RELATÓRIO ---\n";
    $relatorio = getRelatorioContrato($id_contrato);
    echo "Status: " . $relatorio['resultado']['status'] . "\n";
    echo "Total de logs: " . $relatorio['total_logs'] . "\n";
    echo "Tempo de processamento: " . ($relatorio['tempo_processamento'] ?? 'N/A') . " segundos\n";
    
    if (!empty($relatorio['erros'])) {
        echo "Erros encontrados: " . count($relatorio['erros']) . "\n";
        foreach ($relatorio['erros'] as $erro) {
            echo "  - {$erro['flag']}: {$erro['message']}\n";
        }
    }
}

/**
 * Exemplo de uso das funções de consulta
 */
function exemploConsultarLogs($id_contrato) {
    echo "\n--- HISTÓRICO DE LOGS ---\n";
    
    $logs = getHistoricoLogs($id_contrato);
    
    if (empty($logs)) {
        echo "Nenhum log encontrado para o contrato {$id_contrato}\n";
        return;
    }

    foreach ($logs as $log) {
        echo "[{$log['created_at']}] {$log['flag']}: {$log['message']}\n";
        
        // Exibe informações extras se houver
        if (!empty($log['extra_info'])) {
            echo "  Detalhes: " . json_encode($log['extra_info'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
        }
        echo "\n";
    }
}

// Exemplo de uso (descomente para testar)
/*
$id_contrato_teste = 12345;

echo "=== EXEMPLO DE USO DO SISTEMA DE LOGGING ===\n\n";

// Processa um contrato
exemploProcessarContrato($id_contrato_teste);

// Consulta os logs
exemploConsultarLogs($id_contrato_teste);

// Verifica se foi processado
if (contratoJaProcessado($id_contrato_teste)) {
    echo "Contrato {$id_contrato_teste} já foi processado.\n";
    
    $resultado = getResultadoContrato($id_contrato_teste);
    echo "Status atual: " . $resultado['status'] . "\n";
    echo "Passos executados: " . implode(', ', $resultado['passos_executados']) . "\n";
}
*/
