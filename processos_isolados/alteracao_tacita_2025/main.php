<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/helpers.php';

// $contratosIgnorados = getContratosIgnorados();
$contratosIgnorados = [];

main();

function main() {
    $contratosJaExecutados = getContratosJaExecutados();
    $contratosAExecutar = getContratosAExecutar();

    foreach($contratosAExecutar as $contratoAExecutar) {
        $id_contrato = $contratoAExecutar;

        // Ignora contratos já executados
        if (in_array($id_contrato, $contratosJaExecutados))
            continue;

        echo PHP_EOL . "Executando alteração do contrato $id_contrato...";
        executarAlteracao($id_contrato);
    }
}

function executarAlteracao($id_contrato) {
    $passos_executados = [];

    // Log início da alteração
    logAlteracao($id_contrato, 'INICIO', 'Iniciando processo de alteração tácita', [
        'id_contrato' => $id_contrato,
        'timestamp' => date('Y-m-d H:i:s')
    ]);

    global $contratosIgnorados;
    // Ignora contratos que estejam fora do padrão esperado (ver função "getContratosIgnorados")
    foreach($contratosIgnorados as $motivo => $idsContratosIgnorados) {
        if (in_array($id_contrato, $idsContratosIgnorados)) {
            logAlteracao($id_contrato, 'VERIFICACAO_PADRAO', 'Contrato ignorado - fora do padrão', [
                'motivo' => $motivo,
                'id_contrato' => $id_contrato
            ]);
            setAborted($id_contrato, $passos_executados, ['Contrato ignorado. Presente em:' => $motivo]);
            return false;
        }
    }

    logAlteracao($id_contrato, 'VERIFICACAO_PADRAO', 'Contrato aprovado para alteração - dentro do padrão', [
        'id_contrato' => $id_contrato
    ]);
    $passos_executados[] = 'Verificação de contratos fora do padrão';

    // ------------------------------------------
    try {

        // $dadosAlteracao = getDadosAlteracao($id_contrato);
        $dadosAlteracao = [
            'id_novo_plano_venda' => 759,
            'id_integracao_tv' => 5,
        ];

        // --------------------------------------------
        $dadosPre = salvarDadosPre($id_contrato);
        if (!$dadosPre) {
            logAlteracao($id_contrato, 'SALVAR_DADOS_PRE', 'Falha ao salvar dados pré-alteração', [
                'id_contrato' => $id_contrato,
                'erro' => 'Função salvarDadosPre retornou false'
            ]);
            throw new Exception('Falha ao salvar os dados pré-alteração');
        }

        logAlteracao($id_contrato, 'SALVAR_DADOS_PRE', 'Dados pré-alteração salvos com sucesso', [
            'id_contrato' => $id_contrato,
            'dados_salvos' => $dadosPre
        ]);
        $passos_executados[] = 'salvarDadosPre';
        // --------------------------------------------

        // --------------------------------------------
        $atualizacaoContrato = atualizarContrato($id_contrato, $dadosAlteracao['id_novo_plano_venda']);
        if (!$atualizacaoContrato) {
            logAlteracao($id_contrato, 'ATUALIZAR_CONTRATO', 'Falha ao atualizar contrato', [
                'id_contrato' => $id_contrato,
                'id_novo_plano_venda' => $dadosAlteracao['id_novo_plano_venda'],
                'plano_venda_anterior' => $dadosPre['plano_venda'],
                'erro' => 'Função atualizarContrato retornou false'
            ]);
            throw new Exception('Falha ao atualizar o contrato');
        }

        logAlteracao($id_contrato, 'ATUALIZAR_CONTRATO', 'Contrato atualizado com sucesso', [
            'id_contrato' => $id_contrato,
            'plano_venda_anterior' => $dadosPre['plano_venda'],
            'plano_venda_novo' => $dadosAlteracao['id_novo_plano_venda'],
            'data_renovacao_anterior' => $dadosPre['data_renovacao'],
            'data_expiracao_anterior' => $dadosPre['data_expiracao']
        ]);
        $passos_executados[] = 'atualizarContrato';
        // --------------------------------------------

        // --------------------------------------------
        $id_novo_plano_velocidade = getPlanoVelocidadeByPlanoVenda($dadosAlteracao['id_novo_plano_venda']);
        $atualizacaoPlanoVelocidade = atualizarPlanoVelocidade($id_contrato, $id_novo_plano_velocidade);
        if (!$atualizacaoPlanoVelocidade) {
            logAlteracao($id_contrato, 'ATUALIZAR_PLANO_VELOCIDADE', 'Falha ao atualizar plano de velocidade', [
                'id_contrato' => $id_contrato,
                'id_novo_plano_velocidade' => $id_novo_plano_velocidade,
                'plano_velocidade_anterior' => $dadosPre['plano_velocidade'],
                'erro' => 'Função atualizarPlanoVelocidade retornou false'
            ]);
            throw new Exception('Falha ao atualizar o plano de velocidade no login');
        }

        logAlteracao($id_contrato, 'ATUALIZAR_PLANO_VELOCIDADE', 'Plano de velocidade atualizado com sucesso', [
            'id_contrato' => $id_contrato,
            'plano_velocidade_anterior' => $dadosPre['plano_velocidade'],
            'plano_velocidade_novo' => $id_novo_plano_velocidade
        ]);
        $passos_executados[] = 'atualizarPlanoVelocidade';
        // --------------------------------------------

        // --------------------------------------------
        $faturas_abertas = $dadosPre['ids_faturas_abertas'] ? json_decode($dadosPre['ids_faturas_abertas'], true) : [];
        $restauracaoFaturasAbertas = restaurarFaturasAbertas($faturas_abertas);
        if (!$restauracaoFaturasAbertas) {
            logAlteracao($id_contrato, 'RESTAURAR_FATURAS', 'Falha ao restaurar faturas abertas', [
                'id_contrato' => $id_contrato,
                'ids_faturas_abertas' => $faturas_abertas,
                'erro' => 'Função restaurarFaturasAbertas retornou false'
            ]);
            throw new Exception('Falha ao restaurar faturas abertas');
        }

        logAlteracao($id_contrato, 'RESTAURAR_FATURAS', 'Faturas abertas restauradas com sucesso', [
            'id_contrato' => $id_contrato,
            'ids_faturas_restauradas' => $faturas_abertas,
            'quantidade_faturas' => count($faturas_abertas)
        ]);
        $passos_executados[] = 'restaurarFaturasAbertas';
        // --------------------------------------------

        // --------------------------------------------
        $valorFinalPos = getValorFinalContrato($id_contrato);
        // --------------------------------------------

        // --------------------------------------------
        $insercaoDescontosAcrescimos = inserirDescontosAcrescimos($id_contrato, $dadosPre['valor_final'], $valorFinalPos);
        if (!$insercaoDescontosAcrescimos) {
            logAlteracao($id_contrato, 'INSERIR_DESCONTOS_ACRESCIMOS', 'Falha ao inserir descontos/acréscimos', [
                'id_contrato' => $id_contrato,
                'valor_pre' => floatval($dadosPre['valor_final']),
                'valor_pos' => floatval($valorFinalPos),
                'diferenca' => floatval($valorFinalPos) - floatval($dadosPre['valor_final']),
                'erro' => 'Função inserirDescontosAcrescimos retornou false'
            ]);
            throw new Exception('Falha ao inserir descontos e acréscimos');
        }

        $descontosAcrescimosText = getDescontosAcrescimosText($dadosPre['valor_final'], $valorFinalPos);

        logAlteracao($id_contrato, 'INSERIR_DESCONTOS_ACRESCIMOS', 'Descontos/acréscimos inseridos com sucesso', [
            'id_contrato' => $id_contrato,
            'valor_pre' => floatval($dadosPre['valor_final']),
            'valor_pos' => floatval($valorFinalPos),
            'diferenca' => floatval($valorFinalPos) - floatval($dadosPre['valor_final']),
            'descricao_ajuste' => $descontosAcrescimosText
        ]);
        $passos_executados[] = 'inserirDescontosAcrescimos';
        // --------------------------------------------

        // --------------------------------------------
        $criacaoUsuarioTv = criarUsuarioTv($id_contrato, $dadosAlteracao['id_integracao_tv']);
        if (!$criacaoUsuarioTv) {
            logAlteracao($id_contrato, 'CRIAR_USUARIO_TV', 'Falha ao criar usuário TV', [
                'id_contrato' => $id_contrato,
                'id_integracao_tv' => $dadosAlteracao['id_integracao_tv'],
                'erro' => 'Função criarUsuarioTv retornou false'
            ]);
            throw new Exception('Falha ao criar usuário de TV');
        }

        logAlteracao($id_contrato, 'CRIAR_USUARIO_TV', 'Usuário TV criado com sucesso', [
            'id_contrato' => $id_contrato,
            'id_integracao_tv' => $dadosAlteracao['id_integracao_tv'],
            'plataforma' => 'oletv'
        ]);
        $passos_executados[] = 'criarUsuarioTv';
        // --------------------------------------------

        // --------------------------------------------
        $dadosPos = salvarDadosPos($id_contrato);
        if (!$dadosPos) {
            logAlteracao($id_contrato, 'SALVAR_DADOS_POS', 'Falha ao salvar dados pós-alteração', [
                'id_contrato' => $id_contrato,
                'erro' => 'Função salvarDadosPos retornou false'
            ]);
            throw new Exception('Falha ao salvar os dados pós-alteração');
        }

        logAlteracao($id_contrato, 'SALVAR_DADOS_POS', 'Dados pós-alteração salvos com sucesso', [
            'id_contrato' => $id_contrato,
            'dados_salvos' => $dadosPos
        ]);
        $passos_executados[] = 'salvarDadosPos';
        // --------------------------------------------

        // --------------------------------------------
        $insercaoAtendimento = inserirAtendimentoAlteracao($id_contrato, $passos_executados, $dadosPre, $descontosAcrescimosText);
        if (!$insercaoAtendimento) {
            logAlteracao($id_contrato, 'INSERIR_ATENDIMENTO', 'Falha ao inserir atendimento', [
                'id_contrato' => $id_contrato,
                'erro' => 'Função inserirAtendimentoAlteracao retornou false'
            ]);
            throw new Exception('Falha ao inserir atendimento');
        }

        logAlteracao($id_contrato, 'INSERIR_ATENDIMENTO', 'Atendimento inserido com sucesso', [
            'id_contrato' => $id_contrato,
            'id_assunto' => 4463,
            'titulo' => 'ALTERAÇÃO TÁCITA 06/2025 - Ajuste automático de contrato'
        ]);
        $passos_executados[] = 'inserirAtendimentoAlteracao';
        // --------------------------------------------

        // --------------------------------------------
        logAlteracao($id_contrato, 'SUCESSO', 'Alteração tácita concluída com sucesso', [
            'id_contrato' => $id_contrato,
            'passos_executados' => $passos_executados,
            'descontos_acrescimos' => $descontosAcrescimosText,
            'dados_pre' => $dadosPre,
            'dados_pos' => $dadosPos
        ]);

        setExecuted($id_contrato, $passos_executados, [
            'descontos_acrescimos' => $descontosAcrescimosText
        ]);
        // --------------------------------------------

    }
    catch (Exception $e) {
        logAlteracao($id_contrato, 'ERRO', 'Erro durante alteração tácita', [
            'id_contrato' => $id_contrato,
            'erro' => $e->getMessage(),
            'passos_executados' => $passos_executados,
            'linha_erro' => $e->getLine(),
            'arquivo_erro' => $e->getFile()
        ]);

        setFailed($id_contrato, $passos_executados, [
            'erro' => $e->getMessage()
        ]);
        return false;
    }
}

// ------------------------------------------------------------------------
// ------------------------------------------------------------------------

function atualizarContrato($id_contrato, $id_novo_plano_venda) {
    $contrato = getContrato($id_contrato);

    $contrato['id_vd_contrato'] = $id_novo_plano_venda;

    $hoje = new DateTime();
    $contrato['data_renovacao'] = $hoje->format('Y-m-d');
    $hoje->modify('+12 months');
    $contrato['data_expiracao'] = $hoje->format('Y-m-d');

    $update = updateContrato($contrato);

    if ($update && isset($update['type'])) {
        if ($update['type'] !== 'success') {
            if (isset($update['message']))
                throw new Exception("Erro ao atualizar o contrato: $update[message]");
            else
                throw new Exception("Erro desconhecido ao atualizar o contrato");
        }
    } else {
        throw new Exception("Erro desconhecido ao atualizar o contrato");
    }

    return true;
}

function criarUsuarioTv($id_contrato, $id_integracao) {
    $idProdutoLifeLine = getIdProdutoLifeLine($id_contrato);
    if (!$idProdutoLifeLine)
        throw new Exception('Produto LIFE LINE não encontrado para o contrato ' . $id_contrato);

    $emailPrincipal = getEmailPrincipalByIdContrato($id_contrato);
    if (!$emailPrincipal)
        throw new Exception('Email principal não encontrado para o contrato ' . $id_contrato);

    $params = array(
        "id_contrato" => $id_contrato,
        "plataforma" => "oletv",
        "connection_type_tv" => "STR",
        "id_integracao" => $id_integracao,
        "controle_dos_pais" => "N",

        "id_vd_contratos_produtos" => $idProdutoLifeLine,
        "usar_email_principal" => "N",
        "login" => $emailPrincipal,
        "mac_devices" => "",
        "account_number" => "",
        "id_equipamentos" => "",
        "online" => "1"
    );

    $api = getIxcApi();
    $api->post('tv_usuarios', $params);
    $apiResponse = $api->getRespostaConteudo(true);

    return handleApiResponse($apiResponse, "inserir o usuário OléTV no contrato");
}

// ----------------------------------------------------
// ----------------------------------------------------

function getContratosIgnorados() {
    $contratosProRata = getContratosProRata();
    $contratosDescontosAcrescimos = getContratosDescontosAcrescimos();
    $contratosSemFaturasValorCorreto = getContratosSemFaturasValorCorreto();
    $contratosMaisDeUmProdutoInternet = getContratosMaisDeUmProdutoInternet();

    return [
        'Contratos com serviço adicional por pro rata' => $contratosProRata,
        'Contratos que possuem descontos ou acréscimos' => $contratosDescontosAcrescimos,
        'Contratos sem faturas no valor total dos produtos' => $contratosSemFaturasValorCorreto,
        'Contratos com mais de um produto de internet' => $contratosMaisDeUmProdutoInternet,
    ];
}

function getNovoPlanoVenda($id_plano_venda_atual, $cidade)
{
    global $dbh_noc;

    $dbh_noc->prepare("SELECT id_plano_venda_novo FROM temp.alteracao_tacita_2025 WHERE id_plano_venda_atual = ? AND cidade = ?;");
    $dbh_noc->execute([$id_plano_venda_atual, $cidade]);

    $novoPlanoVenda = $dbh_noc->fetch(PDO::FETCH_COLUMN);

    return $novoPlanoVenda;
}

function salvarDadosPre($id_contrato)
{
    return salvarDadosContrato($id_contrato, 'pre');
}

function salvarDadosPos($id_contrato)
{
    return salvarDadosContrato($id_contrato, 'pos');
}

function atualizarPlanoVelocidade($id_contrato, $id_novo_plano_velocidade) {
    $radusuario = getRadusuarioByIdContrato($id_contrato);
    if (!$radusuario)
        throw new Exception('Radusuario não encontrado para o contrato ' . $id_contrato);

    $radusuario['id_grupo'] = $id_novo_plano_velocidade;

    $update = updateRadusuario($radusuario);

    if ($update && isset($update['type'])) {
        if ($update['type'] !== 'success') {
            if (isset($update['message']))
                throw new Exception("Erro ao atualizar o radusuario: $update[message]");
            else
                throw new Exception("Erro desconhecido ao atualizar o radusuario");
        }
    } else {
        throw new Exception("Erro desconhecido ao atualizar o radusuario");
    }

    return true;
}

function restaurarFaturasAbertas($ids_faturas_abertas) {
    // Se não há faturas para restaurar, retorna true
    if (empty($ids_faturas_abertas) || !is_array($ids_faturas_abertas)) {
        return true;
    }

    foreach($ids_faturas_abertas as $id_fatura) {
        $fatura = getFatura($id_fatura);
        if (!$fatura) {
            throw new Exception("Fatura $id_fatura não encontrada");
        }

        $fatura['status'] = 'A';
        $fatura['valor_cancelado'] = 0;
        $fatura['id_mot_cancelamento'] = 0;

        $update = updateFatura($fatura);

        if ($update && isset($update['type'])) {
            if ($update['type'] !== 'success') {
                if(isset($update['message']))
                    throw new Exception("Erro ao restaurar fatura $id_fatura: $update[message]");
                else
                    throw new Exception("Erro desconhecido ao restaurar fatura $id_fatura");
            }
        } else {
            throw new Exception("Erro desconhecido ao restaurar fatura $id_fatura");
        }
    }

    return true;
}

function getContratosAExecutar() {
    return [110741];
}

function inserirDescontosAcrescimos($id_contrato, $valorPre, $valorPos) {
    $valorPre = floatval($valorPre);
    $valorPos = floatval($valorPos);

    $valorDiferencial = $valorPos - $valorPre;

    if ($valorDiferencial == 0)
        return true;

    else if ($valorDiferencial > 0) {
        $tipoDescontoAcrescimo = $tipoTxt = 'desconto';
    }

    else if ($valorDiferencial < 0) {
        $tipoDescontoAcrescimo = 'acrescimo';
        $tipoTxt = 'acréscimo';
    }

    $valor = abs($valorDiferencial);
    $percentual = (abs($valorDiferencial) / $valorPos) * 100;

    $data = date('d/m/Y');
    $descricao = "Alteração tácita $data - $tipoTxt automático";

    $produtoInternet = getIdProdutoInternet($id_contrato);

    return inserirDescontoOuAcrescimo(
        $tipoDescontoAcrescimo,
        [
            'id_contrato' => $id_contrato,
            'id_vd_contrato_produtos' => $produtoInternet,
            'valor' => $valor,
            'percentual' => $percentual,
            'descricao' => $descricao,
            'data_validade' => '0000-00-00',
        ]
    );
}

function getDescontosAcrescimosText($valorPre, $valorPos) {
    $valorPre = floatval($valorPre);
    $valorPos = floatval($valorPos);

    $valorDiferencial = $valorPos - $valorPre;

    if ($valorDiferencial == 0)
        return "Nenhum desconto ou acréscimo inserido";

    else if ($valorDiferencial > 0) {
        $tipoTxt = 'desconto';
    }

    else if ($valorDiferencial < 0) {
        $tipoTxt = 'acréscimo';
    }

    $valor = abs($valorDiferencial);
    $percentual = (abs($valorDiferencial) / $valorPos) * 100;

    $valorTxt = number_format($valor, 2, ',', '.');
    $percentualTxt = number_format($percentual, 2, ',', '.');

    return "Inserido $tipoTxt de R$$valorTxt ($percentualTxt%) no produto de internet";
}

function inserirAtendimentoAlteracao($id_contrato, $passos_executados, $dadosPre, $descontosAcrescimosText = "") {
    $mensagem = getMensagemAtendimento($id_contrato, $passos_executados, $dadosPre, $descontosAcrescimosText);

    return inserirAtendimentoContrato($id_contrato, [
        'id_assunto' => 4463,
        'id_ticket_setor' => 1, // Setor padrão
        'titulo' => 'ALTERAÇÃO TÁCITA 06/2025 - Ajuste automático de contrato',
        'mensagem' => $mensagem
    ]);
}