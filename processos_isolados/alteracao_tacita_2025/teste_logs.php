<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/helpers.php';

// Teste do sistema de logs
$id_contrato_teste = 65491;

echo "=== TESTE DO SISTEMA DE LOGS ===" . PHP_EOL;

// Teste 1: Log simples
echo "1. Testando log simples..." . PHP_EOL;
$resultado1 = logAlteracao($id_contrato_teste, 'TESTE', 'Teste de log simples', [
    'teste' => true,
    'timestamp' => date('Y-m-d H:i:s')
]);
echo "Resultado: " . ($resultado1 ? "SUCESSO" : "ERRO") . PHP_EOL . PHP_EOL;

// Teste 2: Log com dados complexos
echo "2. Testando log com dados complexos..." . PHP_EOL;
$dadosComplexos = [
    'id_contrato' => $id_contrato_teste,
    'dados_pre' => [
        'plano_venda' => 661,
        'plano_velocidade' => 1129,
        'valor_final' => 52.44
    ],
    'dados_pos' => [
        'plano_venda' => 662,
        'plano_velocidade' => 1130,
        'valor_final' => 52.44
    ],
    'alteracoes' => [
        'plano_venda_alterado' => true,
        'plano_velocidade_alterado' => true,
        'valor_mantido' => true
    ]
];

$resultado2 = logAlteracao($id_contrato_teste, 'TESTE_COMPLEXO', 'Teste com dados complexos para rollback', $dadosComplexos);
echo "Resultado: " . ($resultado2 ? "SUCESSO" : "ERRO") . PHP_EOL . PHP_EOL;

// Teste 3: Teste da função getMensagemAtendimento
echo "3. Testando função getMensagemAtendimento..." . PHP_EOL;
$dadosPre = [
    'id_contrato' => $id_contrato_teste,
    'plano_venda' => 661,
    'plano_velocidade' => 1129,
    'data_renovacao' => '2023-09-12',
    'data_expiracao' => '2024-09-12',
    'valor_final' => 52.44,
    'cidade_instalacao' => 'Poços de Caldas',
    'ids_faturas_abertas' => '[123, 456, 789]'
];

$passos_executados = [
    'Verificação de contratos fora do padrão',
    'salvarDadosPre',
    'atualizarContrato',
    'atualizarPlanoVelocidade',
    'restaurarFaturasAbertas',
    'inserirDescontosAcrescimos',
    'criarUsuarioTv',
    'salvarDadosPos',
    'inserirAtendimentoAlteracao'
];

$descontosAcrescimosText = "Inserido desconto de R$10,00 (16,05%) no produto de internet";

try {
    $mensagem = getMensagemAtendimento($id_contrato_teste, $passos_executados, $dadosPre, $descontosAcrescimosText);
    echo "Mensagem gerada com sucesso!" . PHP_EOL;
    echo "Tamanho da mensagem: " . strlen($mensagem) . " caracteres" . PHP_EOL;
    echo "Primeiras 200 caracteres:" . PHP_EOL;
    echo substr($mensagem, 0, 200) . "..." . PHP_EOL . PHP_EOL;
} catch (Exception $e) {
    echo "ERRO ao gerar mensagem: " . $e->getMessage() . PHP_EOL . PHP_EOL;
}

echo "=== TESTE CONCLUÍDO ===" . PHP_EOL;
echo "Verifique os logs na tabela alteracao_tacita_2025.log" . PHP_EOL;
