# Alteração Tácita 2025 - Sistema de Logs e Mensagens

## Melhorias Implementadas

### 1. Sistema de Logs Detalhado

Foi implementado um sistema completo de logs na tabela `alteracao_tacita_2025.log` para facilitar rollbacks e auditoria.

#### Estrutura da Tabela de Logs
```sql
CREATE TABLE alteracao_tacita_2025.log (
    id serial4 NOT NULL,
    created_at timestamp NULL,
    id_contrato int4 NULL,
    flag varchar NULL,
    message varchar NULL,
    extra_info json NULL,
    CONSTRAINT log_pk PRIMARY KEY (id)
);
```

#### Função de Log
```php
logAlteracao($id_contrato, $flag, $message, $extra_info = [])
```

**Parâmetros:**
- `$id_contrato`: ID do contrato sendo alterado
- `$flag`: Identificador do passo/ação (ex: 'INICIO', 'ATUALIZAR_CONTRATO', 'ERRO')
- `$message`: Mensagem descritiva da ação
- `$extra_info`: Array com informações detalhadas (convertido para JSON)

#### Flags de Log Implementadas
- `INICIO`: Início do processo de alteração
- `VERIFICACAO_PADRAO`: Verificação se contrato está dentro do padrão
- `SALVAR_DADOS_PRE`: Salvamento dos dados pré-alteração
- `ATUALIZAR_CONTRATO`: Atualização do plano de venda
- `ATUALIZAR_PLANO_VELOCIDADE`: Atualização do plano de velocidade
- `RESTAURAR_FATURAS`: Restauração de faturas abertas
- `INSERIR_DESCONTOS_ACRESCIMOS`: Inserção de ajustes de valor
- `CRIAR_USUARIO_TV`: Criação do usuário OléTV
- `SALVAR_DADOS_POS`: Salvamento dos dados pós-alteração
- `INSERIR_ATENDIMENTO`: Inserção do atendimento
- `SUCESSO`: Conclusão bem-sucedida
- `ERRO`: Erro durante o processo

### 2. Função getMensagemAtendimento Finalizada

A função foi completamente reescrita para incluir todos os passos da alteração com detalhes dos dados pré e pós alteração.

#### Características:
- ✅ Verifica todos os 9 passos da alteração
- ✅ Mostra dados antes e depois quando relevante
- ✅ Inclui detalhes de valores, datas e IDs
- ✅ Formata valores monetários corretamente
- ✅ Mostra faturas restauradas
- ✅ Inclui resumo final com status
- ✅ Conta passos executados vs esperados

#### Exemplo de Saída:
```
Atualização tácita da composição dos planos (planejamento tributário) com aumento de velocidade. Contrato 12 meses. O valor do plano foi mantido.

DETALHES DA ALTERAÇÃO:
=====================
✓ Verificação de contratos fora do padrão: contrato aprovado para alteração.
✓ Backup dos dados pré-alteração: dados salvos com sucesso.
  - Plano de venda anterior: 661
  - Plano de velocidade anterior: 1129
  - Valor final anterior: R$ 52,44
  - Data renovação anterior: 12/09/2023
  - Data expiração anterior: 12/09/2024
✓ Atualizar plano de venda: contrato atualizado para o novo plano de venda.
  - Plano anterior: 661 → Novo plano: 662
  - Data renovação: 12/09/2023 → 16/06/2025
  - Data expiração: 12/09/2024 → 16/06/2026
...

RESUMO FINAL:
=============
Cidade: Poços de Caldas
Passos executados com sucesso: 9/9
Status: CONCLUÍDO COM SUCESSO
```

### 3. Logs Integrados em Todas as Funções

Cada passo da função `executarAlteracao()` agora registra logs detalhados:

#### Dados Registrados para Rollback:
- **Dados pré-alteração**: Todos os dados originais do contrato
- **Dados pós-alteração**: Estado final após todas as alterações
- **IDs alterados**: Planos de venda, velocidade, produtos, etc.
- **Valores**: Diferenças de preços, descontos/acréscimos aplicados
- **Faturas**: IDs das faturas restauradas
- **Erros**: Detalhes completos de falhas com linha e arquivo

### 4. Tratamento de Erros Melhorado

- Logs de erro incluem stack trace completo
- Informações de rollback em caso de falha
- Validações adicionais (ex: faturas existentes)

## Como Usar

### Executar Alteração
```php
executarAlteracao($id_contrato);
```

### Consultar Logs
```sql
SELECT * FROM alteracao_tacita_2025.log 
WHERE id_contrato = 65491 
ORDER BY created_at;
```

### Testar Sistema
```bash
php processos_isolados/alteracao_tacita_2025/teste_logs.php
```

## Rollback

Para fazer rollback de um contrato, use os dados salvos nos logs:

1. Consulte os logs do contrato
2. Use os dados do `extra_info` para restaurar:
   - Plano de venda original
   - Plano de velocidade original
   - Datas de renovação/expiração
   - Remover descontos/acréscimos inseridos
   - Remover usuário TV criado

## Arquivos Modificados

- `helpers.php`: Adicionada função `logAlteracao()` e finalizada `getMensagemAtendimento()`
- `main.php`: Integrados logs em todos os passos da `executarAlteracao()`
- `teste_logs.php`: Script de teste do sistema (novo)
- `README.md`: Documentação das melhorias (novo)
