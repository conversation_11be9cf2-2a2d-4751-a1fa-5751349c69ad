# Sistema de Logging Consolidado - Alteração Tácita 2025

## Visão Geral

O sistema de logging foi refatorado para PostgreSQL e consolidado em uma única função principal (`setResultado`) com logging detalhado e informações para rollback.

## Principais Melhorias

### 1. Função Consolidada `setResultado()`
- Substitui as três funções anteriores (`setExecuted`, `setAborted`, `setFailed`)
- Sintaxe PostgreSQL corrigida (UPSERT com `ON CONFLICT`)
- Logging detalhado automático
- Suporte a dados before/after para rollback

### 2. Logging Detalhado
- Campo `extra_info` JSON com dados estruturados
- Informações de rollback automáticas
- Timestamps precisos com microtime
- Rastreamento de exceções completo

### 3. Funções Auxiliares de Logging
- `logInicioFuncao()` - Log de início de funções
- `logFimFuncao()` - Log de fim com tempo de execução
- `logAlteracaoDados()` - Log de alterações com dados before/after

### 4. Funções de Consulta
- `getHistoricoLogs()` - Recupera histórico completo
- `getResultadoContrato()` - Status atual do contrato
- `contratoJaProcessado()` - Verifica se foi processado
- `getRelatorioContrato()` - Relatório resumido

## Como Usar

### Definindo Resultados

```php
// Dados antes das alterações
$dados_antes = getDadosContrato($id_contrato);

// ... executa alterações ...

// Dados depois das alterações  
$dados_depois = getDadosContrato($id_contrato);

// Define resultado com logging automático
setExecuted($id_contrato, $passos_executados, $info_adicional, $dados_antes, $dados_depois);
// ou
setFailed($id_contrato, $passos_executados, $info_adicional, $dados_antes, $dados_depois);
// ou  
setAborted($id_contrato, $passos_executados, $info_adicional, $dados_antes, $dados_depois);
```

### Logging de Funções

```php
function minhaFuncao($id_contrato, $parametro) {
    $tempo_inicio = microtime(true);
    
    // Log de início
    logInicioFuncao($id_contrato, 'minhaFuncao', [
        'parametro' => $parametro
    ]);

    try {
        // ... lógica da função ...
        
        // Log de sucesso
        logFimFuncao($id_contrato, 'minhaFuncao', true, $resultado, $tempo_inicio);
        return $resultado;
        
    } catch (Exception $e) {
        // Log de erro
        logFimFuncao($id_contrato, 'minhaFuncao', false, [
            'erro' => $e->getMessage()
        ], $tempo_inicio);
        throw $e;
    }
}
```

### Logging de Alterações de Dados

```php
// Antes da alteração
$dados_antes = buscarDadosAtuais($id);

// Executa alteração
$stmt = $dbh->prepare("UPDATE tabela SET campo = ? WHERE id = ?");
$stmt->execute([$novo_valor, $id]);

// Depois da alteração
$dados_depois = buscarDadosAtuais($id);

// Log da alteração
logAlteracaoDados($id_contrato, 'UPDATE_TABELA', 'tabela', $dados_antes, $dados_depois, "id = {$id}");
```

### Consultando Logs

```php
// Verifica se foi processado
if (contratoJaProcessado($id_contrato)) {
    echo "Contrato já processado";
}

// Recupera resultado atual
$resultado = getResultadoContrato($id_contrato);
echo "Status: " . $resultado['status'];

// Histórico completo
$logs = getHistoricoLogs($id_contrato);
foreach ($logs as $log) {
    echo $log['flag'] . ': ' . $log['message'];
}

// Relatório resumido
$relatorio = getRelatorioContrato($id_contrato);
echo "Total de logs: " . $relatorio['total_logs'];
echo "Erros: " . count($relatorio['erros']);
```

## Estrutura do Extra Info

O campo `extra_info` contém dados estruturados em JSON:

```json
{
    "status": "executed",
    "passos_executados": ["passo1", "passo2"],
    "dados_antes": { "campo": "valor_anterior" },
    "dados_depois": { "campo": "valor_novo" },
    "rollback_info": {
        "tabela": "cliente_contrato",
        "dados_originais": { "campo": "valor_anterior" },
        "condicao_restauracao": "id = 123"
    },
    "tempo_execucao_segundos": 1.234,
    "timestamp": "2025-01-15 10:30:45"
}
```

## Flags de Log Padronizadas

- `INICIO_[funcao]` - Início de função
- `SUCESSO_[funcao]` - Sucesso na função
- `ERRO_[funcao]` - Erro na função
- `EXCEPTION_[funcao]` - Exceção na função
- `DATA_CHANGE_[tipo]` - Alteração de dados
- `SET_RESULTADO_[status]` - Definição de resultado

## Benefícios para Rollback

Cada alteração registra:
- Dados originais completos
- Condição WHERE para restauração
- Timestamp da alteração
- Contexto da operação

Isso permite criar scripts de rollback automáticos baseados nos logs.

## Exemplo Completo

Veja o arquivo `exemplo_uso_logging.php` para um exemplo completo de como usar todas as funcionalidades.

## Migração do Código Existente

### Antes:
```php
setExecuted($id_contrato, $passos, $info);
```

### Depois:
```php
$dados_antes = getDadosContrato($id_contrato);
// ... alterações ...
$dados_depois = getDadosContrato($id_contrato);
setExecuted($id_contrato, $passos, $info, $dados_antes, $dados_depois);
```

As funções antigas ainda funcionam (retrocompatibilidade), mas recomenda-se usar a nova assinatura para aproveitar todos os benefícios.
