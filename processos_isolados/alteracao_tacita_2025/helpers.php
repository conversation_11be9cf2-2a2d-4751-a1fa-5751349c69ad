<?php
require_once __DIR__ . '/../../common/config.php';

$dbh_ixc = getConnectionIxc();
$dbh_noc = getConnectionNoc();

function getDadosContrato($id_contrato) {
    global $dbh_ixc;

    $stmt = $dbh_ixc->prepare("SELECT cc.id AS id_contrato,
        cc.id_vd_contrato AS plano_venda,
        ru.id_grupo AS plano_velocidade,
        cc.data_renovacao,
        cc.data_expiracao,
        SUM(vcp.valor_unit) AS valor_produtos,
        SUM(
            (CASE WHEN cca.valor > 0 THEN cca.valor ELSE 0 END)
        ) AS valor_acrescimos,
        SUM(
            (CASE WHEN ccd.valor > 0 THEN ccd.valor ELSE 0 END)
        ) AS valor_descontos,
        SUM(
            vcp.valor_unit
            + (CASE WHEN cca.valor > 0 THEN cca.valor ELSE 0 END)
            - (CASE WHEN ccd.valor > 0 THEN ccd.valor ELSE 0 END)
        ) AS valor_final,
        (CASE
            WHEN ru.endereco_padrao_cliente = 'S' THEN ci3.nome
            WHEN ru.endereco_padrao_cliente = 'N' AND ru.cidade != 0 AND ru.cidade IS NOT NULL THEN ci1.nome
            WHEN cc.endereco_padrao_cliente = 'N' AND cc.cidade != 0 AND cc.cidade IS NOT NULL THEN ci2.nome
            ELSE ci3.nome
        END) AS cidade_instalacao,
        ci3.nome AS cidade_cliente,
        (
            SELECT JSON_ARRAYAGG(ar.id)
            FROM fn_areceber ar 
            WHERE ar.id_contrato = cc.id AND ar.status = 'A'
        ) AS ids_faturas_abertas
    FROM cliente_contrato cc
    LEFT JOIN cliente cl ON cl.id = cc.id_cliente
    LEFT JOIN radusuarios ru ON ru.id_contrato = cc.id
    LEFT JOIN vd_contratos_produtos vcp ON vcp.id_contrato = cc.id OR vcp.id_vd_contrato = cc.id_vd_contrato
    LEFT JOIN cliente_contrato_acrescimos cca ON cca.id_vd_contrato_produtos = vcp.id AND cca.id_contrato = cc.id AND (cca.data_validade = '0000-00-00' OR cca.data_validade IS NULL OR cca.data_validade > NOW())
    LEFT JOIN cliente_contrato_descontos ccd ON ccd.id_vd_contrato_produtos = vcp.id AND ccd.id_contrato = cc.id AND (ccd.data_validade = '0000-00-00' OR ccd.data_validade IS NULL OR ccd.data_validade > NOW())
    LEFT JOIN cidade ci1 ON ci1.id = ru.cidade
    LEFT JOIN cidade ci2 ON ci2.id = cc.cidade
    LEFT JOIN cidade ci3 ON ci3.id = cl.cidade
    WHERE cc.id = ?;");

    $stmt->execute([$id_contrato]);

    $dados = $stmt->fetch(PDO::FETCH_ASSOC);

    return $dados;
}

function salvarDadosContrato($id_contrato, $type) {
    global $dbh_noc;

    if (!in_array($type, ['pre', 'pos']))
        return false;

    $table = $type == 'pre' ? 'dados_pre' : 'dados_pos';

    $dadosContrato = getDadosContrato($id_contrato);

    $stmt = $dbh_noc->prepare("INSERT INTO alteracao_tacita_2025.$table (id_contrato, plano_venda, plano_velocidade, data_renovacao, data_expiracao, valor_produtos, valor_acrescimos, valor_descontos, valor_final, cidade_instalacao, cidade_cliente, ids_faturas_abertas) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);");
    $query = $stmt->execute([
        $dadosContrato['id_contrato'],$dadosContrato['plano_venda'], $dadosContrato['plano_velocidade'], $dadosContrato['data_renovacao'], $dadosContrato['data_expiracao'], $dadosContrato['valor_produtos'], $dadosContrato['valor_acrescimos'], $dadosContrato['valor_descontos'], $dadosContrato['valor_final'], $dadosContrato['cidade_instalacao'], $dadosContrato['cidade_cliente'], $dadosContrato['ids_faturas_abertas']
    ]);

    if (!$query)
        throw new Exception('Erro ao salvar dados pre');

    return $dadosContrato;
}

/**
 * Define o resultado como executado com sucesso
 * @param int $id_contrato ID do contrato
 * @param array|string $passos_executados Array ou string JSON com os passos executados
 * @param array|string $info Array ou string JSON com informações extras
 * @return bool
 */
function setExecuted($id_contrato, $passos_executados = null, $info = null) {
    global $dbh_noc;

    // Converte arrays para JSON se necessário
    $passos_json = is_array($passos_executados) ? json_encode($passos_executados,) : $passos_executados;
    $info_json = is_array($info) ? json_encode($info) : $info;

    try {
        $stmt = $dbh_noc->prepare("
            INSERT INTO alteracao_tacita_2025.resultados
            (id_contrato, status, passos_executados, info)
            VALUES (?, 'executed', ?, ?)
            ON DUPLICATE KEY UPDATE
            status = 'executed',
            passos_executados = ?,
            info = ?,
            created_at = NOW()
        ");

        return $stmt->execute([
            $id_contrato,
            $passos_json,
            $info_json,
            $passos_json,
            $info_json
        ]);
    } catch (Exception $e) {
        error_log("Erro ao definir resultado como executado: " . $e->getMessage());
        return false;
    }
}

/**
 * Define o resultado como abortado
 * @param int $id_contrato ID do contrato
 * @param array|string $passos_executados Array ou string JSON com os passos executados
 * @param array|string $info Array ou string JSON com informações extras
 * @return bool
 */
function setAborted($id_contrato, $passos_executados = null, $info = null) {
    global $dbh_noc;

    // Converte arrays para JSON se necessário
    $passos_json = is_array($passos_executados) ? json_encode($passos_executados, JSON_UNESCAPED_UNICODE | JSON_INVALID_UTF8_IGNORE) : $passos_executados;
    $info_json = is_array($info) ? json_encode($info, JSON_UNESCAPED_UNICODE | JSON_INVALID_UTF8_IGNORE) : $info;

    try {
        $stmt = $dbh_noc->prepare("
            INSERT INTO alteracao_tacita_2025.resultados
            (id_contrato, status, passos_executados, info)
            VALUES (?, 'aborted', ?, ?)
            ON DUPLICATE KEY UPDATE
            status = 'aborted',
            passos_executados = ?,
            info = ?,
            created_at = NOW()
        ");

        return $stmt->execute([
            $id_contrato,
            $passos_json,
            $info_json,
            $passos_json,
            $info_json
        ]);
    } catch (Exception $e) {
        error_log("Erro ao definir resultado como abortado: " . $e->getMessage());
        return false;
    }
}

/**
 * Define o resultado como falhou
 * @param int $id_contrato ID do contrato
 * @param array|string $passos_executados Array ou string JSON com os passos executados
 * @param array|string $info Array ou string JSON com informações extras
 * @return bool
 */
function setFailed($id_contrato, $passos_executados = null, $info = null) {
    global $dbh_noc;

    // Converte arrays para JSON se necessário
    $passos_json = is_array($passos_executados) ? json_encode($passos_executados, JSON_UNESCAPED_UNICODE | JSON_INVALID_UTF8_IGNORE) : $passos_executados;
    $info_json = is_array($info) ? json_encode($info, JSON_UNESCAPED_UNICODE | JSON_INVALID_UTF8_IGNORE) : $info;

    try {
        $stmt = $dbh_noc->prepare("
            INSERT INTO alteracao_tacita_2025.resultados
            (id_contrato, status, passos_executados, info)
            VALUES (?, 'failed', ?, ?)
            ON DUPLICATE KEY UPDATE
            status = 'failed',
            passos_executados = ?,
            info = ?,
            created_at = NOW()
        ");

        return $stmt->execute([
            $id_contrato,
            $passos_json,
            $info_json,
            $passos_json,
            $info_json
        ]);
    } catch (Exception $e) {
        error_log("Erro ao definir resultado como falhou: " . $e->getMessage());
        return false;
    }
}

function getPlanoVelocidadeByPlanoVenda($id_plano_venda) {
    global $dbh_ixc;

    $stmt = $dbh_ixc->prepare("SELECT vcp.id_plano AS id_plano_velocidade
        FROM vd_contratos_produtos vcp
        WHERE vcp.id_vd_contrato = ?
            AND vcp.tipo = 'I';");
    $stmt->execute([$id_plano_venda]);

    $planoVelocidade = $stmt->fetchAll(PDO::FETCH_COLUMN);

    if (sizeof($planoVelocidade) == 0)
        throw new Exception('Plano de velocidade não encontrado');
    elseif (sizeof($planoVelocidade) > 1)
        throw new Exception('Mais de um plano de velocidade encontrado');

    return $planoVelocidade[0];
}




function getContratosProRata()
{
    global $dbh_ixc;

    $stmt = $dbh_ixc->prepare("SELECT DISTINCT id_contrato
        FROM cliente_contrato_servicos
        WHERE incluido_por_prorata = 'S';");
    $stmt->execute();

    $contratos = $stmt->fetchAll(PDO::FETCH_COLUMN);

    return $contratos;
}

function getContratosDescontosAcrescimos()
{
    global $dbh_ixc;

    $stmt = $dbh_ixc->prepare("SELECT DISTINCT id_contrato
        FROM cliente_contrato_acrescimos cca 
        WHERE cca.data_validade = '0000-00-00' OR cca.data_validade IS NULL OR cca.data_validade > NOW()
        UNION
        SELECT DISTINCT id_contrato
        FROM cliente_contrato_descontos ccd 
        WHERE ccd.data_validade = '0000-00-00' OR ccd.data_validade IS NULL OR ccd.data_validade > NOW();");
    $stmt->execute();

    $contratos = $stmt->fetchAll(PDO::FETCH_COLUMN);

    return $contratos;
}

function getContratosJaExecutados() {
    global $dbh_noc;

    $stmt = $dbh_noc->prepare("SELECT DISTINCT id_contrato
    FROM alteracao_tacita_2025.resultados;");
    $stmt->execute();

    $contratos = $stmt->fetchAll(PDO::FETCH_COLUMN);

    return $contratos;
}

function getContratosSemFaturasValorCorreto() {
    global $dbh_ixc;

    $stmt = $dbh_ixc->prepare("SELECT id_contrato FROM (
        SELECT sub.id_contrato, sub.valor_final, ar.id FROM (
            SELECT cc.id AS id_contrato,
                SUM(
                    (vcp.valor_unit * vcp.qtde)
                    + (CASE WHEN cca.valor > 0 THEN cca.valor ELSE 0 END)
                    - (CASE WHEN ccd.valor > 0 THEN ccd.valor ELSE 0 END)
                ) AS valor_final
            FROM cliente_contrato cc
            LEFT JOIN (
                SELECT vcp.id, cc.id AS id_contrato, vcp.descricao, vcp.valor_unit, vcp.qtde
                FROM cliente_contrato cc
                INNER JOIN vd_contratos_produtos vcp ON vcp.id_vd_contrato = cc.id_vd_contrato
                WHERE cc.id_vd_contrato != 0 AND cc.id_vd_contrato != '' AND cc.id_vd_contrato IS NOT NULL	
                UNION	
                SELECT vcp.id, vcp.id_contrato, vcp.descricao, vcp.valor_unit, vcp.qtde
                FROM cliente_contrato cc
                INNER JOIN vd_contratos_produtos vcp ON vcp.id_contrato = cc.id
            ) vcp ON vcp.id_contrato = cc.id
            LEFT JOIN cliente_contrato_acrescimos cca ON cca.id_vd_contrato_produtos = vcp.id AND cca.id_contrato = cc.id AND (cca.data_validade = '0000-00-00' OR cca.data_validade IS NULL OR cca.data_validade > NOW())
            LEFT JOIN cliente_contrato_descontos ccd ON ccd.id_vd_contrato_produtos = vcp.id AND ccd.id_contrato = cc.id AND (ccd.data_validade = '0000-00-00' OR ccd.data_validade IS NULL OR ccd.data_validade > NOW())
            WHERE cc.status NOT IN ('D', 'I')
            GROUP BY 1
        ) sub
        LEFT JOIN fn_areceber ar ON ar.id_contrato = sub.id_contrato
            AND ar.valor = sub.valor_final
        ) sub2
    WHERE sub2.id IS NULL;");

    $stmt->execute();

    $contratos = $stmt->fetchAll(PDO::FETCH_COLUMN);

    return $contratos;
}

function getContratosMaisDeUmProdutoInternet() {
    global $dbh_ixc;

    $stmt = $dbh_ixc->prepare("SELECT cc.id AS id_contrato
        FROM cliente_contrato cc
        LEFT JOIN (
            SELECT vcp.id, cc.id AS id_contrato
            FROM cliente_contrato cc
            INNER JOIN vd_contratos_produtos vcp ON vcp.id_vd_contrato = cc.id_vd_contrato AND vcp.tipo = 'I'
            WHERE cc.id_vd_contrato != 0 AND cc.id_vd_contrato != '' AND cc.id_vd_contrato IS NOT NULL	
            UNION	
            SELECT vcp.id, vcp.id_contrato
            FROM cliente_contrato cc
            INNER JOIN vd_contratos_produtos vcp ON vcp.id_contrato = cc.id AND vcp.tipo = 'I'
        ) vcp ON vcp.id_contrato = cc.id
        WHERE cc.status NOT IN ('D', 'I')
        GROUP BY 1
        HAVING count(*) > 1;");

    $stmt->execute();

    $contratos = $stmt->fetchAll(PDO::FETCH_COLUMN);

    return $contratos;
}

/**
 * Registra um log detalhado na tabela alteracao_tacita_2025.log
 * @param int $id_contrato ID do contrato
 * @param string $flag Flag identificadora do passo/ação
 * @param string $message Mensagem descritiva
 * @param array $extra_info Informações extras em formato array (será convertido para JSON)
 * @return bool
 */
function logAlteracao($id_contrato, $flag, $message, $extra_info = []) {
    global $dbh_noc;

    try {
        $stmt = $dbh_noc->prepare("
            INSERT INTO alteracao_tacita_2025.log
            (created_at, id_contrato, flag, message, extra_info)
            VALUES (NOW(), ?, ?, ?, ?)
        ");

        $extra_info_json = json_encode($extra_info, JSON_UNESCAPED_UNICODE | JSON_INVALID_UTF8_IGNORE);

        if (!$extra_info_json){
            echo PHP_EOL . '----------------------' . PHP_EOL;
            switch (json_last_error()) {
                case JSON_ERROR_NONE:
                    echo ' - No errors';
                break;
                case JSON_ERROR_DEPTH:
                    echo ' - Maximum stack depth exceeded';
                break;
                case JSON_ERROR_STATE_MISMATCH:
                    echo ' - Underflow or the modes mismatch';
                break;
                case JSON_ERROR_CTRL_CHAR:
                    echo ' - Unexpected control character found';
                break;
                case JSON_ERROR_SYNTAX:
                    echo ' - Syntax error, malformed JSON';
                break;
                case JSON_ERROR_UTF8:
                    echo ' - Malformed UTF-8 characters, possibly incorrectly encoded';
                break;
                default:
                    echo ' - Unknown error';
                break;
            }
            echo PHP_EOL . '----------------------' . PHP_EOL;
        }

        return $stmt->execute([
            $id_contrato,
            $flag,
            $message,
            $extra_info_json
        ]);
    } catch (Exception $e) {
        error_log("Erro ao registrar log de alteração: " . $e->getMessage());
        return false;
    }
}

function getMensagemAtendimento($id_contrato, $passos_executados, $dadosPre, $descontosAcrescimosText = "") {
    $dadosPos = getDadosContrato($id_contrato);

    $mensagem = 'Atualização tácita da composição dos planos (planejamento tributário) com aumento de velocidade. Contrato 12 meses. O valor do plano foi mantido.' . PHP_EOL . PHP_EOL;

    $mensagem .= "DETALHES DA ALTERAÇÃO:" . PHP_EOL;
    $mensagem .= "=====================" . PHP_EOL;

    // --------------------------------------------
    // VERIFICAÇÃO DE CONTRATOS FORA DO PADRÃO
    // --------------------------------------------
    if (in_array('Verificação de contratos fora do padrão', $passos_executados)) {
        $mensagem .= '✓ Verificação de contratos fora do padrão: contrato aprovado para alteração.' . PHP_EOL;
    } else {
        $mensagem .= '✗ (ERRO) Verificação de contratos fora do padrão: contrato não passou na verificação.' . PHP_EOL;
    }

    // --------------------------------------------
    // ATUALIZAR CONTRATO
    // --------------------------------------------
    if (in_array('atualizarContrato', $passos_executados)) {
        $mensagem .= '✓ Atualizar plano de venda: contrato atualizado para o novo plano de venda.' . PHP_EOL;
        if (isset($dadosPos['plano_venda']) && isset($dadosPre['plano_venda'])) {
            $mensagem .= "  - Plano anterior: {$dadosPre['plano_venda']} → Novo plano: {$dadosPos['plano_venda']}" . PHP_EOL;
        }
    } else {
        $mensagem .= '✗ (ERRO) Atualizar plano de venda: contrato não foi atualizado para o novo plano de venda.' . PHP_EOL;
    }

    // --------------------------------------------
    // ATUALIZAR PLANO DE VELOCIDADE
    // --------------------------------------------
    if (in_array('atualizarPlanoVelocidade', $passos_executados)) {
        $mensagem .= '✓ Atualizar plano de velocidade: login do cliente atualizado para o novo plano de velocidade.' . PHP_EOL;
        if (isset($dadosPos['plano_velocidade']) && isset($dadosPre['plano_velocidade'])) {
            $mensagem .= "  - Plano velocidade anterior: {$dadosPre['plano_velocidade']} → Novo plano: {$dadosPos['plano_velocidade']}" . PHP_EOL;
        }
    } else {
        $mensagem .= '✗ (ERRO) Atualizar plano de velocidade: login do cliente não foi atualizado para o novo plano de velocidade.' . PHP_EOL;
    }

    // --------------------------------------------
    // RESTAURAR FATURAS ABERTAS
    // --------------------------------------------
    if (in_array('restaurarFaturasAbertas', $passos_executados)) {
        $mensagem .= '✓ Restaurar faturas abertas: faturas restauradas com sucesso.' . PHP_EOL;
        if (isset($dadosPre['ids_faturas_abertas']) && $dadosPre['ids_faturas_abertas']) {
            $faturas = json_decode($dadosPre['ids_faturas_abertas'], true);
            if (is_array($faturas) && count($faturas) > 0) {
                $mensagem .= "  - Faturas restauradas: " . implode(', ', $faturas) . PHP_EOL;
            }
        }
    } else {
        $mensagem .= '✗ (ERRO) Restaurar faturas abertas: falha ao restaurar faturas.' . PHP_EOL;
    }

    // --------------------------------------------
    // INSERIR DESCONTOS/ACRÉSCIMOS
    // --------------------------------------------
    if (in_array('inserirDescontosAcrescimos', $passos_executados)) {
        $mensagem .= '✓ Ajuste de valores: desconto/acréscimo inserido para manter valor do plano.' . PHP_EOL;
        if ($descontosAcrescimosText != "") {
            $mensagem .= "  - " . $descontosAcrescimosText . PHP_EOL;
        }
    } else {
        $mensagem .= '✗ (ERRO) Ajuste de valores: falha ao inserir desconto/acréscimo.' . PHP_EOL;
    }

    // --------------------------------------------
    // CRIAR USUÁRIO TV
    // --------------------------------------------
    if (in_array('criarUsuarioTv', $passos_executados)) {
        $mensagem .= '✓ Criar usuário TV: usuário OléTV criado com sucesso.' . PHP_EOL;
    } else {
        $mensagem .= '✗ (ERRO) Criar usuário TV: falha ao criar usuário OléTV.' . PHP_EOL;
    }

    $mensagem .= PHP_EOL . "RESUMO FINAL:" . PHP_EOL;
    $mensagem .= "=============" . PHP_EOL;

    if (isset($dadosPre['cidade_instalacao'])) {
        $mensagem .= "Cidade: {$dadosPre['cidade_instalacao']}" . PHP_EOL;
    }

    $passosComSucesso = 0;
    $passosEsperados = ['Verificação de contratos fora do padrão', 'atualizarContrato', 'atualizarPlanoVelocidade', 'restaurarFaturasAbertas', 'inserirDescontosAcrescimos', 'criarUsuarioTv'];

    foreach ($passosEsperados as $passo) {
        if (in_array($passo, $passos_executados)) {
            $passosComSucesso++;
        }
    }

    $mensagem .= "Passos executados com sucesso: {$passosComSucesso}/" . count($passosEsperados) . PHP_EOL;
    $mensagem .= "Status: " . ($passosComSucesso == count($passosEsperados) ? "CONCLUÍDO COM SUCESSO" : "CONCLUÍDO COM ERROS") . PHP_EOL;

    return $mensagem;
}