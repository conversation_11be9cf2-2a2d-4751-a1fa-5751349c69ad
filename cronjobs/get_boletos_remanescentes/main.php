<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/Logger.php';

$f = fopen(__DIR__ . '/lock', 'w') or die('Cannot create lock file');
if (flock($f, LOCK_EX | LOCK_NB)) {
	main();
}

$Logger;

function main() {
	global $Logger;

	$Logger = new Logger('get_boletos_remanescentes');

	$Logger->write([
		'flag' => 'INFO',
		'message' => 'script iniciado'
	]);
	
	$boletosRemanescentes = getBoletosRemanescentes();

	insertBoletoRemanescente($boletosRemanescentes);
}

function getBoletosRemanescentes() {
	$dbh_noc = getConnectionNoc();
	$dbh_ixc = getConnectionIxc();

	$stmt = $dbh_noc->prepare("SELECT id_contrato FROM comercial.boletos_remanescentes;");
	$query = $stmt->execute();
	$ignoredIdContratos = $stmt->fetchAll(PDO::FETCH_COLUMN);
	if(sizeof($ignoredIdContratos) == 0)
		$ignoredIdContratos = [0];

	$ignoredIdContratosStr = implode(", ", $ignoredIdContratos);
	
	$stmt = $dbh_ixc->prepare(
		"SELECT cl.id AS id_cliente,
			cc.id AS id_contrato,
			ar.id AS id_fatura,
			ar.id_saida,
			cl.razao AS nome_cliente,
			ar.data_emissao,
			ar.data_vencimento,
			ar.valor,
			u.nome AS operador
		FROM ixc_logs l
		INNER JOIN cliente_contrato cc ON cc.id = TRIM(BOTH '\"' FROM json_extract(l.campos, '$.id_contrato'))
			AND cc.data_cancelamento = DATE(l.data)
			AND cc.id NOT IN ($ignoredIdContratosStr)
		LEFT JOIN cliente cl ON cl.id = TRIM(BOTH '\"' FROM json_extract(l.campos, '$.id_cliente'))
		LEFT JOIN fn_areceber ar ON ar.id_saida = l.id_tabela
		LEFT JOIN usuarios u ON u.id = l.operador
		WHERE l.tabela = 'vd_saida'
			AND l.campos LIKE '%id_cliente%'
			AND l.campos NOT LIKE '{\"gera_estoque%'
			AND l.campos LIKE '%\"id_tipo_documento\":\"501\"%'
		ORDER BY l.id DESC;"
	);
	$result = $stmt->execute();
	$boletosRemanescentes = $stmt->fetchAll(PDO::FETCH_ASSOC);

	return $boletosRemanescentes;
}

function insertBoletoRemanescente($boletosRemanescentes) {
	if(sizeof($boletosRemanescentes) < 1)
		return true;

	$values = '';
	foreach($boletosRemanescentes as $boleto) {
		$values .= "($boleto[id_cliente], $boleto[id_contrato], $boleto[id_fatura], $boleto[id_saida], '$boleto[nome_cliente]', '$boleto[data_emissao]', '$boleto[data_vencimento]', $boleto[valor], '$boleto[operador]'),";
	}
	$values = trim($values, ',');

	$dbh_noc = getConnectionNoc();

	$stmt = $dbh_noc->prepare("INSERT INTO comercial.boletos_remanescentes (id_cliente, id_contrato, id_fatura, id_saida, nome_cliente, data_emissao, data_vencimento, valor, operador) VALUES $values;");
	
	$query = $stmt->execute();

	return $query;
}