<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/Logger.php';
$dbh_noc = getConnectionNoc();

// -------------------------------------------------------------
echo "Coletando primeiro vendedor de cada contrato..." . PHP_EOL;

// Limpa os que estiverem NULL no banco
cleanVendedoresNull();
$contratos = getContratosIxc();
$totalContratos = count($contratos);

echo "Total de contratos pendentes: $totalContratos" . PHP_EOL;
$progress = 0;
foreach ($contratos as $contrato) {
    $primeiroVendedor = getPrimeiroVendedor($contrato['id_contrato']);

    $stmt = $dbh_noc->prepare("INSERT INTO comercial.primeiro_vendedor_contratos (id_contrato, id_vendedor) VALUES (:id_contrato, :id_vendedor);");
    $stmt->bindParam(':id_contrato', $contrato['id_contrato']);
    $stmt->bindParam(':id_vendedor', $primeiroVendedor);
    $stmt->execute();

    $progress++;
    echo "Progresso: $progress/$totalContratos (" . round(($progress / $totalContratos) * 100, 2) . "%)\r";
}

// -------------------------------------------------------------

function getPrimeiroVendedor($id_contrato)
{
    $api = getIxcApi();
    $params = array(
        'qtype' => 'ixc_logs.id_tabela',
        'query' => '',
        'oper' => '=',
        'sortname' => 'ixc_logs.data',
        'sortorder' => 'asc',
        'page' => '1',
        'rp' => '200',
        'grid_param' => json_encode([
            [
                'TB' => 'ixc_logs.tabela',
                'OP' => '=',
                'P' => 'cliente_contrato'
            ],
            [
                'TB' => 'ixc_logs.id_tabela',
                'OP' => '=',
                'P' => $id_contrato
            ]
        ])
    );

    $api->get('ixc_logs', $params);
    $retorno = $api->getRespostaConteudo(true);

    $registros = $retorno['registros'];

    foreach ($registros as $registro) {
        $campos = json_decode(utf8_encode($registro["campos"]));

        if (isset($campos->id_vendedor) && $campos->id_vendedor != "0") {
            $primeiroVendedor = $campos->id_vendedor;
            return $primeiroVendedor;
        }
    }
}

// ---------------------------------------

function getContratosIxc()
{
    global $dbh_noc;
    $stmt = $dbh_noc->prepare("SELECT cc.id AS id_contrato,
            cc.id_vendedor_ativ,
            v.nome AS vendedor
        FROM fdwtables.ixc_cliente_contrato cc
        LEFT JOIN fdwtables.ixc_vendedor v ON v.id = cc.id_vendedor_ativ
        WHERE cc.data_cadastro_sistema >= '2021-04-12'
            AND cc.id NOT IN (
                SELECT id_contrato
                FROM comercial.primeiro_vendedor_contratos
                WHERE id_vendedor IS NOT NULL
            );");
    $stmt->execute();
    $contratos = $stmt->fetchAll();

    return $contratos;
}

// Remove os que não puderam ser consultados nas execuções anteriores (contratos novos ou esquecimento do preenchimento do campo do vendedor), para tentar consultar novamente
function cleanVendedoresNull() {
    global $dbh_noc;

    $stmt = $dbh_noc->prepare("DELETE
        FROM comercial.primeiro_vendedor_contratos
        WHERE id_vendedor IS NULL;");

    return $stmt->execute();
}