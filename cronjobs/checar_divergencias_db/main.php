<?php
require_once __DIR__ . '/../../common/autoload.php';

$f = fopen(__DIR__ . '/lock', 'w') or die('Cannot create lock file');
if (!flock($f, LOCK_EX | LOCK_NB)) {
    die('Cannot acquire lock');
}

$Logger = new Logger('checar_divergencias_db');

$dbhIxc = getConnectionIxc();
$dbhNoc = getConnectionNoc();

initializeLogger();
main();
finalizeLogger();

function main()
{
    global $Logger;

    $currentSchema = getCurrentSchema();
    $lastSchemaInfo = getLastSchemaInfo();

    $lastSchema = json_decode($lastSchemaInfo['db_schema'], true);
    $lastSchemaDate = $lastSchemaInfo['created_at'];

    // $currentSchema['cliente_contrato'][] = '`data_assinatura` varchar(100) DEFAULT NULL';

    $schemaChanges = getSchemaChanges($currentSchema, $lastSchema);

    if (sizeof($schemaChanges) == 0) {
        $Logger->write([
            'flag' => 'INFO',
            'message' => 'Não houve modificações na estrutura do banco de dados ixcprovedor.'
        ]);
        echo 'Não houve modificações na estrutura do banco de dados ixcprovedor.';
        return;
    }

    $Logger->write([
        'flag' => 'WARNING',
        'message' => 'Houve modificações no banco de dados ixcprovedor: `' . json_encode($schemaChanges) . '`'
    ]);

    sendChangesEmail($lastSchemaDate, $schemaChanges);

    insertSchemaChanges($currentSchema, $schemaChanges);
}

// ---------------------------------

function initializeLogger() {
    global $Logger;
    $Logger->write([
        'flag' => 'INFO',
        'message' => 'script iniciado'
    ]);
}

function finalizeLogger() {
    global $Logger;
    $Logger->write([
        'flag' => 'INFO',
        'message' => 'script finalizado'
    ]);
}

function getCurrentSchema()
{
    // Get all table names
    $tables = getIxcTableNames();

    // Build JSON schema with table columns
    $currentSchema = [];
    foreach ($tables as $table) {
        $columns = getTableColumns($table);
        if ($columns !== null) {
            $currentSchema[$table] = $columns;
        }
    }

    return $currentSchema;
}

function getLastSchemaInfo()
{
    global $dbhNoc;
    $stmt = $dbhNoc->query("SELECT db_schema, created_at FROM fdwtables.ixc_schema_monitor ORDER BY id DESC LIMIT 1");
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

function areObjectsEqual($currentschema, $lastschema)
{
    if (array_keys($currentschema) !== array_keys($lastschema)) {
        return false;
    }
    foreach ($currentschema as $key => $value) {
        if (array_diff($value, $lastschema[$key])) {
            return false;
        }
    }
    return true;
}

function getSchemaChanges($currentschema, $lastschema)
{
    // Separate arrays for each type of change
    $removedTables = [];
    $modifiedTables = [];
    $addedTables = [];

    // Arrays to track which tables have removed columns (for sorting later)
    $tablesWithRemovedColumns = [];

    $addedKeys = array_diff_key($currentschema, $lastschema);
    $removedKeys = array_diff_key($lastschema, $currentschema);
    $modifiedKeys = array_intersect_key($currentschema, $lastschema);

    // Process removed tables first
    foreach ($removedKeys as $key => $value) {
        $removedTables[] = "Tabela '$key' removida: " . PHP_EOL . json_encode($value, JSON_PRETTY_PRINT);
    }

    // Process modified tables and collect information about each table
    foreach ($modifiedKeys as $key => $value) {
        $hasRemovedColumns = false;
        $tableChanges = "Tabela '$key' modificada:";
        $changeDetails = [];

        $addedItems = array_diff($currentschema[$key], $lastschema[$key]);
        $removedItems = array_diff($lastschema[$key], $currentschema[$key]);

        // Check for removed columns
        if (!empty($removedItems)) {
            $changeDetails[] = "Colunas removidas: " . PHP_EOL . json_encode(array_values($removedItems), JSON_PRETTY_PRINT);
            $hasRemovedColumns = true;
        }

        // Check for added columns
        if (!empty($addedItems)) {
            var_dump($addedItems);
            $changeDetails[] = "Colunas adicionadas: " . PHP_EOL . json_encode(array_values($addedItems), JSON_PRETTY_PRINT);

            echo "Tabela '$key' modificada: colunas adicionadas: " . PHP_EOL . json_encode(array_values($addedItems), JSON_PRETTY_PRINT);
        }

        // Combine all changes for this table into a single entry
        if (!empty($changeDetails)) {
            $tableChanges .= PHP_EOL . implode(PHP_EOL . PHP_EOL, $changeDetails);
            $modifiedTables[$key] = $tableChanges;

            // Track tables with removed columns for prioritization
            if ($hasRemovedColumns) {
                $tablesWithRemovedColumns[] = $key;
            }
        }
    }

    // Sort modified tables to prioritize those with removed columns
    $sortedModifiedTables = [];

    // First add tables with removed columns
    foreach ($tablesWithRemovedColumns as $tableName) {
        $sortedModifiedTables[] = $modifiedTables[$tableName];
        unset($modifiedTables[$tableName]);
    }

    // Then add remaining tables (those with only added columns)
    foreach ($modifiedTables as $tableName => $tableInfo) {
        $sortedModifiedTables[] = $tableInfo;
    }

    // Process added tables last
    foreach ($addedKeys as $key => $value) {
        $addedTables[] = "Tabela '$key' adicionada: " . PHP_EOL . json_encode($value, JSON_PRETTY_PRINT);
    }

    // Combine arrays in the requested order:
    // 1. removed tables
    // 2. modified tables (prioritizing those with removed columns)
    // 3. added tables
    $differences = array_merge(
        $removedTables,
        $sortedModifiedTables,
        $addedTables
    );

    return $differences;
}

function insertSchemaChanges($currentSchema, $schemaChanges) {
    global $dbhNoc;

    $currentSchemaJson = json_encode($currentSchema);
    $schemaChangesJson = json_encode($schemaChanges);

    $stmt = $dbhNoc->prepare("INSERT INTO fdwtables.ixc_schema_monitor (db_schema, db_schema_changes) VALUES (:db_schema, :db_schema_changes)");
    $stmt->bindParam(':db_schema', $currentSchemaJson);
    $stmt->bindParam(':db_schema_changes', $schemaChangesJson);

    return $stmt->execute();
}

function getIxcTableNames()
{
    global $dbhIxc;

    // Get only base tables, excluding views
    $stmt = $dbhIxc->query("
        SELECT TABLE_NAME
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_TYPE = 'BASE TABLE'
    ");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    return $tables;
}

function getTableColumns($tableName)
{
    global $dbhIxc;
    try {
        $stmt = $dbhIxc->prepare("SHOW CREATE TABLE $tableName");
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        $createTable = $result['Create Table'];

        $columns = [];
        $pattern = '/\`([a-zA-Z0-9_]+)\` ([a-zA-Z0-9() ]+) NOT NULL/';
        preg_match_all($pattern, $createTable, $matches);
        foreach ($matches[1] as $index => $columnName) {
            $columns[] = "`$columnName` {$matches[2][$index]} NOT NULL";
        }

        return $columns;
    } catch (PDOException $e) {
        global $Logger;
        $Logger->write([
            'flag' => 'ERROR',
            'message' => "Failed to get columns for table $tableName: " . $e->getMessage()
        ]);
        return null;
    }
}

function sendChangesEmail($lastSchemaDate, $schemaChanges)
{
	$templatesDir = __DIR__ . '/../../common/MailTemplates';
	$imgDir = __DIR__ . '/../../common/img';

	$mail = getPhpMailer([
		'name' => 'Automação Telemidia',
		'username' => '<EMAIL>',
		'password' => 'whxp#46t5'
	]);

	$mail->Subject = '[Auto] - DB ixcprovedor alterado';
	$mail->addAddress('<EMAIL>');

	$mail->addEmbeddedImage($imgDir . '/logos/light/telemidia_alt.png', 'telemidia_light');

	$mail->addEmbeddedImage($imgDir . '/logos/dark/telemidia.png', 'telemidia_dark');

	$template = new Template($templatesDir . '/COR_Divergencias_ixcprovedor.php');

	// Alimentando o template com as variaveis necessarias nele
	// A variavel $codigoCidade é importante para definir o nome/logotipo/endereco da respectiva empresa, de acordo com a cidade
	$template->set('schemaChanges', $schemaChanges);
    $template->set('lastSchemaDate', $lastSchemaDate);
	$template->set('COMPANY_INFO', json_encode(getCompanyInfo()));

	$body = $template->render();

	$mail->Body    = $body;
	$mail->AltBody = $body;

	if ($mail->send()) {
	}
}