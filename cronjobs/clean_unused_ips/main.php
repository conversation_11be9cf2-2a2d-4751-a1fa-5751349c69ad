<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/Logger.php';

$Logger = new Logger('clean_unused_ips');

$f = fopen(__DIR__ . '/lock', 'w') or die('Cannot create lock file');
if (flock($f, LOCK_EX | LOCK_NB)) {
	$Logger->write([
		'flag' => 'INFO',
		'message' => 'script iniciado'
	]);

	main();

	$Logger->write([
		'flag' => 'INFO',
		'message' => 'script finalizado'
	]);
}
else {
	$Logger->write([
		'flag' => 'INFO',
		'message' => 'script já em execução'
	]);
	die('Cannot create lock file');
	exit;
}

function main() {
	global $Logger;

	$dbh = getConnectionIxc();

	/*
		Antes só eram removidos IPs de logins inativos com contratos inativos
		Porém, aconteciam casos de logins ativos com contratos inativos
		Agora, considera apenas o status do contrato (Desistiu ou Inativo)
	*/
	$sql = "SELECT ru.*
		FROM radusuarios ru
		INNER JOIN cliente_contrato cc ON cc.id = ru.id_contrato
		WHERE (ru.ip != '' OR ru.framed_pd_ipv6 != '' OR ru.pd_ipv6 != '') -- com algum campo de IP preenchido
			AND cc.status IN ('D', 'I'); -- com contrato Desistiu/Inativo";

	$stmt = $dbh->prepare($sql);
	$stmt->execute();

	$radusuarios = $stmt->fetchAll(PDO::FETCH_ASSOC);

	if(sizeof($radusuarios) === 0) {
		$Logger->write([
			'flag' => 'INFO',
			'message' => "finalizando script: nenhum usuário com IP a ser limpo"
		]);
		return;
	}

	$total = count($radusuarios);
	$counter = 0;
	foreach($radusuarios as $radusuario) {
		$counter++;
		echo "Processando $counter de $total logins..." . PHP_EOL;

		$framed_ipv6 = $radusuario['framed_pd_ipv6'];
		$login = $radusuario['login'];

		// No banco dbradius, remove a alocação de IPv6
        deleteRadiusIpv6Allocation($framed_ipv6, $login);

		// No IXC, limpa IPv4, IPv6 e desmarca as opções para preencher IP automático ou vincular IP ao login
		$radusuario['relacionar_ip_ao_login'] = 'N';
		$radusuario['fixar_ip'] = 'N';
		$radusuario['ip'] = '';
		$radusuario['fixar_ipv6'] = 'N';
		$radusuario['pd_ipv6'] = '';
		$radusuario['framed_fixar_ipv6'] = 'N';
		$radusuario['framed_pd_ipv6'] = '';

		$update = updateRadusuario($radusuario);

		if (isset($update['type']) && $update['type'] === 'success') {
			$Logger->write([
				'flag' => 'INFO',
				'login' => $login,
				'message' => "Os IPs foram desalocados do login $login no IXC"
			]);
		}
		else {
			$Logger->write([
				'flag' => 'INFO',
				'login' => $login,
				'message' => "Ocorreu um erro ao desalocar os IPs do login $login no IXC: " . isset($update['message']) ? $update['message'] : 'ERRO DESCONHECIDO'
			]);
		}
	}
}

function deleteRadiusIpv6Allocation($framed_ipv6, $login) {
	if (strlen(trim($framed_ipv6)) == 0)
		return false;

	global $Logger;

	$dbh_radius = getConnectionRadius();

	$stmt = $dbh_radius->prepare("SELECT * FROM radipv6_allocated WHERE allocated_prefix = ?;");
	$stmt->execute([$framed_ipv6]);
	$result = $stmt->fetch(PDO::FETCH_ASSOC);

	if (!isset($result['reserved_prefix']) || strlen(trim($result['reserved_prefix'])) == 0) {
		$Logger->write([
			'flag' => 'INFO',
			'login' => $login,
			'message' => "Este IPv6 do login $login já foi excluído do dbradius: $result[reserved_prefix]"
		]);
		return true;
	}

	$stmt = $dbh_radius->prepare("DELETE FROM radipv6_reserved WHERE reserved_prefix = ?;");
	$query = $stmt->execute([$result['reserved_prefix']]);
	
	if ($query) {
		$Logger->write([
			'flag' => 'INFO',
			'login' => $login,
			'message' => "O IPv6 do login $login foi excluído do dbradius: $result[reserved_prefix]"
		]);
		return true;
	}
	else {
		$Logger->write([
			'flag' => 'INFO',
			'login' => $login,
			'message' => "Erro ao tentar excluir o IPv6 do login $login no dbradius: $result[reserved_prefix]"
		]);
		return false;
	}
}