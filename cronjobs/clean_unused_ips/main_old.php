<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/Logger.php';

$f = fopen(__DIR__ . '/lock', 'w') or die('Cannot create lock file');
if (flock($f, LOCK_EX | LOCK_NB)) {
	main();
}

function main() {
	$dbh = getConnectionIxc();

	$sql = "SELECT ru.*
		FROM radusuarios ru
		INNER JOIN cliente_contrato cc ON cc.id = ru.id_contrato
			AND cc.status IN ('D', 'I')
		WHERE ru.ativo='N'
			AND (ru.framed_pd_ipv6 != '' OR ru.pd_ipv6 != '');";
	$stmt = $dbh->prepare($sql);
	$stmt->execute();

	$radusuarios = $stmt->fetchAll(PDO::FETCH_ASSOC);

	// Collect all framed_ipv6 values
	$framed_ipv6_list = [];
	foreach ($radusuarios as $radusuario) {
		if (!empty($radusuario['framed_pd_ipv6'])) {
			$framed_ipv6_list[] = $radusuario['framed_pd_ipv6'];
		}
	}

	if (count($framed_ipv6_list) > 0) {
		$dbh_radius = getConnectionRadius();

		// Prepare placeholders for IN clause
		$placeholders = implode(',', array_fill(0, count($framed_ipv6_list), '?'));

		// Query radipv6_allocated for all framed_ipv6 at once
		$stmt = $dbh_radius->prepare("SELECT * FROM radipv6_allocated WHERE allocated_prefix IN ($placeholders);");
		$stmt->execute($framed_ipv6_list);
		$allocated_results = $stmt->fetchAll(PDO::FETCH_ASSOC);

		// Collect reserved_prefix values to delete
		$reserved_prefixes = [];
		foreach ($allocated_results as $row) {
			if (isset($row['reserved_prefix']) && strlen(trim($row['reserved_prefix'])) > 0) {
				$reserved_prefixes[] = $row['reserved_prefix'];
			}
		}

		if (count($reserved_prefixes) > 0) {
			// Prepare placeholders for reserved_prefix IN clause
			$placeholders_reserved = implode(',', array_fill(0, count($reserved_prefixes), '?'));

			echo "DELETE FROM radipv6_reserved WHERE reserved_prefix IN ($placeholders_reserved);";

			// Delete all reserved_prefixes in one query
			$stmt_delete = $dbh_radius->prepare("DELETE FROM radipv6_reserved WHERE reserved_prefix IN ($placeholders_reserved);");
			$query = $stmt_delete->execute($reserved_prefixes);

			echo "IPs APAGADOS: " . implode(', ', $reserved_prefixes) . PHP_EOL;
			echo "QUERY: $query" . PHP_EOL;
		} else {
			echo "Nenhum IP reservado para apagar." . PHP_EOL;
		}
	} else {
		echo "Nenhum framed_ipv6 encontrado para processar." . PHP_EOL;
	}

	// Now update radusuario records as before
	$total = count($radusuarios);
	$counter = 0;
	foreach($radusuarios as $radusuario) {
		$counter++;
		// echo "Processing $counter of $total radusuarios..." . PHP_EOL;

		$radusuario['ip'] = '';
		$radusuario['fixar_ip'] = 'N';
		$radusuario['pd_ipv6'] = '';
		$radusuario['fixar_ipv6'] = 'N';
		$radusuario['framed_pd_ipv6'] = '';
		$radusuario['framed_fixar_ipv6'] = 'N';

		$update = updateRadusuario($radusuario);

        echo "$radusuario[login]: $update[message]" . PHP_EOL;
        // var_dump($update);
        // echo PHP_EOL . PHP_EOL;
	}
}
