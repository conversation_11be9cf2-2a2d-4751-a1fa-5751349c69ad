<?php
require_once __DIR__ . '/../../common/autoload.php';

$f = fopen(__DIR__ . '/lock', 'w') or die('Cannot create lock file');
if (flock($f, LOCK_EX | LOCK_NB)) {
	main();
}

$Logger;

function main()
{
	global $Logger;
	$Logger = new Logger('checar_divergencias');
	$Logger->write([
		'flag' => 'INFO',
		'message' => 'script iniciado'
	]);

	// -----------------------------------

	$divergenciasSkyResponse = getDivergenciasSky();

	if (!isset($divergenciasSkyResponse['status']) || $divergenciasSkyResponse['status'] !== 'success')
		throw new Exception('Ocorreu um erro ao buscar as divergências');

	$divergenciasSky = $divergenciasSkyResponse['divergencias'];

	$totalDivergencias = 0;
	foreach ($divergenciasSky as $divergencias)
		$totalDivergencias += count($divergencias);

	if ($totalDivergencias < 1)
		return;

	sendComercialEmail($divergenciasSky);
}

function sendComercialEmail($divergenciasSky)
{
	$templatesDir = __DIR__ . '/../../common/MailTemplates';
	$imgDir = __DIR__ . '/../../common/img';

	$mail = getPhpMailer([
		'name' => 'Automação Telemidia',
		'username' => '<EMAIL>',
		'password' => 'whxp#46t5'
	]);

	$mail->Subject = '[Auto] - Divergências no IXC';
	$mail->addAddress('<EMAIL>');

	$mail->addEmbeddedImage($imgDir . '/logos/light/telemidia_alt.png', 'telemidia_light');

	$mail->addEmbeddedImage($imgDir . '/logos/dark/telemidia.png', 'telemidia_dark');

	$template = new Template($templatesDir . '/Comercial_DivergenciasSky.php');

	// Alimentando o template com as variaveis necessarias nele
	// A variavel $codigoCidade é importante para definir o nome/logotipo/endereco da respectiva empresa, de acordo com a cidade
	$template->set('divergenciasSky', $divergenciasSky);
	$template->set('COMPANY_INFO', json_encode(getCompanyInfo()));
	$template->set('DIVERGENCIAS_SKY_INFO', json_encode(getDivergenciasSkyInfo()));

	$body = $template->render();

	$mail->Body    = $body;
	$mail->AltBody = $body;

	if ($mail->send()) {
	}
}

function getDivergenciasSky()
{
	try {
		$dbh_ixc = getConnectionIxc();
		$dbh_noc = getConnectionNoc();

		$stmt = $dbh_noc->prepare("SELECT dgo.contratos_em_atraso();");
		$stmt->execute();
		$idsContratosFaturasVencidas = $stmt->fetchAll(PDO::FETCH_COLUMN);

		$stmt = $dbh_noc->prepare("SELECT
		  cc.id AS id_contrato,
		  vcp.id_produto,
		  vcp.descricao AS produto,
		  vcp.tipo,
		  p.id_dgo,
		  p.descricao_dgo,
		  cc.status AS status_contrato,
		  cc.status_internet
		FROM fdwtables.ixc_cliente_contrato cc
		INNER JOIN fdwtables.ixc_vd_contratos_produtos vcp ON vcp.id_contrato = cc.id
		  AND (vcp.id_produto IN (SELECT id
				  FROM fdwtables.ixc_produtos
				  WHERE id_sub_grupo = 39))
		LEFT JOIN dgo.pacotes p ON p.id_produto_ixc = vcp.id_produto
		WHERE cc.status NOT IN ('D', 'I')
		UNION
		SELECT cc.id AS id_contrato,
		  vcp.id_produto,
		  vcp.descricao AS produto,
		  vcp.tipo,
		  p.id_dgo,
		  p.descricao_dgo,
		  cc.status AS status_contrato,
		  cc.status_internet
		FROM fdwtables.ixc_cliente_contrato cc
		INNER JOIN fdwtables.ixc_vd_contratos_produtos vcp ON vcp.id_vd_contrato = cc.id_vd_contrato
		  AND (vcp.id_produto IN (SELECT id
				  FROM fdwtables.ixc_produtos
				  WHERE id_sub_grupo = 39))
		LEFT JOIN dgo.pacotes p ON p.id_produto_ixc = vcp.id_produto
		WHERE cc.status NOT IN ('D', 'I');
	  ");
		$stmt->execute();
		$produtosDgo = $stmt->fetchAll(PDO::FETCH_ASSOC);

		$stmt = $dbh_ixc->prepare("SELECT
				tu.id_contrato, tu.login, tu.plataforma,
				cc.status AS status_contrato,
				cc.status_internet
			FROM tv_usuarios tu
			INNER JOIN cliente_contrato cc ON cc.id = tu.id_contrato
				AND cc.status NOT IN ('D', 'I')
			WHERE tu.manually_disabled = 'N'
				AND tu.status_assinante_watch = 1
				AND tu.plataforma != 'oletv';
		");
		$stmt->execute();
		$usuariosTv = $stmt->fetchAll(PDO::FETCH_ASSOC);

		$stmt = $dbh_noc->prepare("SELECT
		  u.email, u.name, u.document,
		  u.id_contrato_ixc, p.id_produto_ixc, p.id_dgo,
		  p.descricao_dgo
		FROM dgo.usuarios u
		LEFT JOIN dgo.pacotes_usuarios pu ON pu.id_usuario = u.id
		LEFT JOIN dgo.pacotes p ON p.id_dgo = pu.id_pacote
		WHERE active = 1;
		");
		$stmt->execute();
		$usuariosAbranet = $stmt->fetchAll(PDO::FETCH_ASSOC);

		// ---------------------------------------------------

		// Arrays utilizados para encontrar as divergências
		$idsContratosComDgo = [];
		$contratosComDgo = [];
		$idsContratosComUsuarioTv = [];
		$usernamesIxc = [];
		$usernamesAbranet = [];
		$userDataAbranet = [];
		$contratoPacotesAbranet = [];
		$contratoPacotesIxc = [];
		$usuariosAbranetSemPacotes = [];

		// Arrays que armazenam as divergências
		$contratosComProdutosTipoIncorreto = [];
		$usuariosTvSemIntegracaoPlayhub = [];
		$contratosDgoSemUsuarioTv = [];
		$usuariosTvSemPacoteDgo = [];
		$usuariosAbranetSemUsuarioIxc = [];
		$usuariosIxcSemUsuarioAbranet = [];
		$contratosComPacotesDivergentes = [];

		foreach ($usuariosAbranet as $usuario) {
			if (!in_array($usuario['email'], $usernamesAbranet))
				$usernamesAbranet[] = $usuario['email'];

			if (
				!isset($contratoPacotesAbranet[$usuario['id_contrato_ixc']])
				&& $usuario['id_contrato_ixc'] !== ''
			)
				$contratoPacotesAbranet[$usuario['id_contrato_ixc']] = [];

			/* Popula o array de usuários Abranet que não possuem pacotes,
		para não serem listados nas divergências de "usuariosAbranetSemUsuarioIxc",
		pois o usuário continua cadastrado na Abranet, porém sem assinaturas vinculadas,
		quando o pacote DGO é cancelado no IXC */
			if (!$usuario['id_dgo'])
				$usuariosAbranetSemPacotes[] = $usuario['email'];

			// Popula o array com os IDs dos pacotes DGO que constam na Abranet
			if (isset($contratoPacotesAbranet[$usuario['id_contrato_ixc']]))
				$contratoPacotesAbranet[$usuario['id_contrato_ixc']][] = $usuario['descricao_dgo'];

			$userDataAbranet[$usuario['email']] = $usuario;
		}

		foreach ($usuariosTv as $usuario) {
			if (!in_array($usuario['id_contrato'], $idsContratosComUsuarioTv))
				$idsContratosComUsuarioTv[] = $usuario['id_contrato'];

			if (!in_array($usuario['login'], $usernamesIxc))
				$usernamesIxc[] = $usuario['login'];

			// Usuários de TV sem integração Playhub
			if ($usuario['plataforma'] !== 'playhub')
				$usuariosTvSemIntegracaoPlayhub[] = [
					'id_contrato' => $usuario['id_contrato'],
					'usuario' => $usuario['login']
				];

			// Usuário TV consta no IXC mas não consta na Abranet
			if (
				!in_array($usuario['login'], $usernamesAbranet)
				&& !in_array($usuario['id_contrato'], $idsContratosFaturasVencidas)
			)
				$usuariosIxcSemUsuarioAbranet[] = [
					'id_contrato' => $usuario['id_contrato'],
					'usuario' => $usuario['login']
				];
		}

		foreach ($produtosDgo as $produto) {
			if (!in_array($produto['id_contrato'], $idsContratosComDgo))
				$idsContratosComDgo[] = $produto['id_contrato'];

			// Popula array com dados dos contratos, para verificação dos status dos contratos
			if (!isset($contratosComDgo[$produto['id_contrato']]))
				$contratosComDgo[$produto['id_contrato']] = $produto;

			// Produto no contrato não está com tipo "TV/Streaming"
			if (
				$produto['tipo'] !== 'TV'
				&& !in_array($produto['id_contrato'], $contratosComProdutosTipoIncorreto)
			) {
				$contratosComProdutosTipoIncorreto[] = $produto['id_contrato'];
			}

			if (
				!isset($contratoPacotesIxc[$produto['id_contrato']])
				&& $produto['id_contrato']
			)
				$contratoPacotesIxc[$produto['id_contrato']] = [];

			// Popula o array com os IDs dos pacotes DGO que constam no IXC
			$contratoPacotesIxc[$produto['id_contrato']][] = $produto['descricao_dgo'];
		}

		// Contratos ativos com pacotes DGO, sem usuários de TV ativos no IXC
		foreach ($idsContratosComDgo as $idContrato) {
			if (
				!in_array($idContrato, $idsContratosComUsuarioTv)
				&& $contratosComDgo[$idContrato]['status_internet'] === 'A'
			)
				$contratosDgoSemUsuarioTv[] = $idContrato;
		}

		// Usuários de TV ativos no IXC, sem pacotes DGO no contrato
		foreach ($idsContratosComUsuarioTv as $idContrato) {
			if (!in_array($idContrato, $idsContratosComDgo))
				$usuariosTvSemPacoteDgo[] = $idContrato;
		}

		// Usuário consta na Abranet (e possui pacotes), mas não possui usuário de TV no IXC
		// Usuário na Abranet sem pacotes é normal, em casos de cancelamentos da DGO
		foreach ($usernamesAbranet as $username) {
			if (
				!in_array($username, $usernamesIxc)
				&& !in_array($username, $usuariosAbranetSemPacotes)
			) {
				$userData = $userDataAbranet[$username];
				$usuariosAbranetSemUsuarioIxc[] = [
					'email' => $userData['email'],
					'name' => $userData['name'],
					'document' => $userData['document']
				];
			}
		}

		// Contratos com pacotes divergentes entre IXC e Abranet
		foreach ($contratoPacotesIxc as $contratoId => $pacotesIxc) {
			/* Caso o acesso não esteja ativo, não entra na verificação de divergências de pacotes,
		  pois o pacote é removido temporariamente da Abranet, quando o cliente fica inadimplente */
			if ($contratosComDgo[$contratoId]['status_internet'] !== 'A')
				continue;

			if (isset($contratoPacotesAbranet[$contratoId])) {
				$pacotesAbranet = $contratoPacotesAbranet[$contratoId];

				foreach ($pacotesIxc as $i => $pacote) {
				  if (trim($pacote) == '')
					unset($pacotesIxc[$i]);
				}
	  
				foreach ($pacotesAbranet as $i => $pacote) {
				  if (trim($pacote) == '')
					unset($pacotesAbranet[$i]);
				}

				sort($pacotesIxc);
				sort($pacotesAbranet);

				// Verifica se os pacotes são diferentes
				if ($pacotesIxc !== $pacotesAbranet) {
					$contratosComPacotesDivergentes[] = [
						'id_contrato' => $contratoId,
						'pacotes_ixc' => implode(', ', $pacotesIxc),
						'pacotes_abranet' => implode(', ', $pacotesAbranet)
					];
				}
			}
		}

		// Contratos com pacotes na Abranet, mas que não possuem SKY+ no IXC
		foreach($contratoPacotesAbranet as $id_contrato => $pacotesAbranet) {
			// Ignora pacotes na Abranet que estejam com "nome vazio"
			foreach ($pacotesAbranet as $i => $pacote) {
				if (trim($pacote) == '')
				unset($pacotesAbranet[$i]);
			}

			if (!isset($contratoPacotesIxc[$id_contrato]) && sizeof($pacotesAbranet) > 0) {
				$contratosComPacotesDivergentes[] = [
					'id_contrato' => $id_contrato,
					'pacotes_ixc' => '[NENHUM]',
					'pacotes_abranet' => implode(', ', $pacotesAbranet)
				];
			}
		}

		// ---------------------------------------------------

		$resposta = array(
			'status' => 'success',
			'divergencias' => [
				'usuariosTvSemIntegracaoPlayhub' => $usuariosTvSemIntegracaoPlayhub,
				'contratosComProdutosTipoIncorreto' => $contratosComProdutosTipoIncorreto,
				'contratosDgoSemUsuarioTv' => $contratosDgoSemUsuarioTv,
				'usuariosTvSemPacoteDgo' => $usuariosTvSemPacoteDgo,
				'usuariosAbranetSemUsuarioIxc' => $usuariosAbranetSemUsuarioIxc,
				'usuariosIxcSemUsuarioAbranet' => $usuariosIxcSemUsuarioAbranet,
				'contratosComPacotesDivergentes' => $contratosComPacotesDivergentes
			]
		);
	} catch (Exception $e) {

		$resposta = array(
			"status" => "error",
			"mensagem" => "Ocorreu um erro ao persistir os dados.",
			"dados" => $e->getMessage()
		);
	}

	return $resposta;
}
