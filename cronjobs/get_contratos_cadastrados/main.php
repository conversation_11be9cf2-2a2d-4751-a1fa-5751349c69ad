<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/Logger.php';

$f = fopen(__DIR__ . '/lock', 'w') or die('Cannot create lock file');
if (flock($f, LOCK_EX | LOCK_NB)) {
	main();
}

$Logger;

function main()
{
	global $Logger;

	$Logger = new Logger('get_contratos_cadastrados');

	try {
		$Logger->write([
			'flag' => 'INFO',
			'message' => 'script iniciado'
		]);

		$api = getIxcApi();

		$cadastrados = getContratosPendentes();
		if (!$cadastrados || sizeof($cadastrados) == 0) {
			$Logger->write([
				'flag' => 'INFO',
				'message' => 'Não há novos contratos a serem inseridos'
			]);
			return;
		}

		$params = array(
			'qtype' => 'ixc_logs.id_tabela',
			'query' => '',
			'oper' => '!=',
			'sortname' => 'ixc_logs.id',
			'sortorder' => 'asc',
			'page' => '1',
			'rp' => '10000',
			'grid_param' =>
			json_encode([
				[
					'TB' => 'ixc_logs.tabela',
					'OP' => '=',
					'P' => 'cliente_contrato'
				],
				[
					'TB' => 'ixc_logs.tipo',
					'OP' => '=',
					'P' => 'inseriu'
				],
				[
					'TB' => 'ixc_logs.id_tabela',
					'OP' => 'IN',
					'P' => implode(',', $cadastrados)
				]
			])
		);

		$api->get('ixc_logs', $params);
		$retorno = $api->getRespostaConteudo(true);

		$registros = isset($retorno['registros']) ? $retorno['registros'] : null;

		if (!$registros && count($registros) == 0) {
			throw new Exception('Ocorreu um erro ao consultar o operador que inseriu os contratos');
		}

		$values = '';
		foreach ($registros as $registro) {
			$values .= "($registro[id_tabela],'" . utf8_encode($registro['operador']) . "'),";
		}
		$values = rtrim($values, ',');

		$sql = "INSERT INTO comercial.log_cadastros_contratos (id_contrato, atendente) VALUES $values;";

		$dbh_noc = getConnectionNoc();
		$stmt = $dbh_noc->prepare($sql);
		$query = $stmt->execute();

		if (!$query)
			throw new Exception('Ocorreu um erro ao executar a query');

		$Logger->write([
			'flag' => 'INFO',
			'message' => 'Script finalizado com sucesso. Contratos consultados: ' . count($registros)
		]);

	} catch (Exception $e) {
		$Logger->write([
			'flag' => 'ERROR',
			'message' => $e->getMessage()
		]);
	}
}

function getContratosPendentes()
{
	$dbh_noc = getConnectionNoc();

	$stmt = $dbh_noc->prepare("SELECT id
        FROM fdwtables.ixc_cliente_contrato
        WHERE
            data_cadastro_sistema NOT LIKE '0000%'
            AND data_cadastro_sistema LIKE '20%'
            AND NULLIF(data_cadastro_sistema::TEXT, '0000-00-00'::TEXT)::DATE BETWEEN '2021-09-20' AND NOW()
            AND id NOT IN (
                SELECT id_contrato FROM comercial.log_cadastros_contratos
            );");

	$stmt->execute();

	$idsContratosPendentes = $stmt->fetchAll(PDO::FETCH_COLUMN);
	if (sizeof($idsContratosPendentes) == 0)
		$idsContratosPendentes = [];

	return $idsContratosPendentes;
}
