<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/Logger.php';

$f = fopen(__DIR__ . '/lock', 'w') or die('Cannot create lock file');
if (flock($f, LOCK_EX | LOCK_NB)) {
	main();
}

$Logger;

function main() {
	global $Logger;

	$Logger = new Logger('get_contratos_cancelados');

	$Logger->write([
		'flag' => 'INFO',
		'message' => 'script iniciado'
	]);
	
	$cancelados = getCancelados();

	foreach($cancelados as $codigo) {
		$details = getCancelamentoDetails($codigo);
		// var_dump($details);
		if(!$details)
			continue;
		
		insertCancelamento($details);
	}
}

function getCancelados() {
	$dbh_noc = getConnectionNoc();
	$dbh_ixc = getConnectionIxc();

	$stmt = $dbh_noc->prepare("SELECT id_contrato FROM comercial.contratos_cancelados;");
	$query = $stmt->execute();
	$ignoredIdContratos = $stmt->fetchAll(PDO::FETCH_COLUMN);
	if(sizeof($ignoredIdContratos) == 0)
		$ignoredIdContratos = [0];

	$ignoredIdContratosStr = implode(", ", $ignoredIdContratos);

	$cancelamentosDesde = '2021-09-20';
	$stmt = $dbh_ixc->prepare("SELECT id FROM cliente_contrato WHERE data_cancelamento BETWEEN '$cancelamentosDesde' AND NOW() AND id NOT IN ($ignoredIdContratosStr);");
	$result = $stmt->execute();
	$cancelados = $stmt->fetchAll(PDO::FETCH_COLUMN);

	return $cancelados;
}

function insertCancelamento($details) {
	$dbh_noc = getConnectionNoc();

	$stmt = $dbh_noc->prepare("INSERT INTO comercial.contratos_cancelados (id_contrato, cliente, cidade, data_cancelamento, id_motivo, motivo_cancelamento, vendedor, atendente, valor) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?);");
	
	$query = $stmt->execute([
		$details['id_contrato'],
		$details['cliente'],
		$details['cidade'],
		$details['data_cancelamento'],
		$details['id_motivo'],
		$details['motivo_cancelamento'],
		$details['vendedor'],
		$details['atendente'],
		$details['valor']
	]);

	return $query;
}

function getCancelamentoDetails($codigo) {
	$dbh_ixc = getConnectionIxc();

	$stmt = $dbh_ixc->prepare("
		SELECT 
			cc.id AS id_contrato,
			cl.razao AS cliente,
			(CASE
				WHEN ci1.nome IS NOT NULL THEN ci1.nome
				WHEN ci2.nome IS NOT NULL THEN ci2.nome
				ELSE NULL
			END) AS cidade,
			cc.data_cancelamento,
			cc.motivo_cancelamento AS id_motivo,
			mc.motivo AS motivo_cancelamento,
			u.nome AS atendente,
			v.nome AS vendedor,
			(CASE WHEN vc.valor_contrato > 0 THEN
				vc.valor_contrato
			ELSE
				SUM(vcp.valor_unit)
			END) AS valor
		FROM ixc_logs l
		INNER JOIN cliente_contrato cc ON cc.id = l.id_tabela
		INNER JOIN cliente cl ON cl.id = cc.id_cliente
		LEFT JOIN vd_contratos vc ON vc.id = cc.id_vd_contrato
		LEFT JOIN vd_contratos_produtos vcp ON vcp.id_contrato = cc.id
		LEFT JOIN fn_areceber_mot_cancelamento mc ON mc.id = cc.motivo_cancelamento
		LEFT JOIN usuarios u ON u.id = l.operador
		LEFT JOIN cidade ci1 ON ci1.id = cc.cidade
		LEFT JOIN cidade ci2 ON ci2.id = cl.cidade
		LEFT JOIN vendedor v ON v.id = cc.id_vendedor
		WHERE l.id = (SELECT MIN(l.id)
					FROM ixc_logs l
					INNER JOIN cliente_contrato cc ON cc.id = l.id_tabela
							AND cc.id = ?
					WHERE l.tabela = 'cliente_contrato'
						AND l.campos LIKE CONCAT('%\"data_cancelamento\":\"', cc.data_cancelamento, '\"%'));
		");

		$query = $stmt->execute(array($codigo));
		$dados = $stmt->fetch(PDO::FETCH_ASSOC);

		if (!$dados['atendente'])
			$dados['atendente'] = 'OPERACAO DO SISTEMA';

		if ($dados['id_contrato'])
			return $dados;
		else
			return null;
}