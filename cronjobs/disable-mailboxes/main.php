<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/Logger.php';

$Logger = new Logger('disable-mailboxes');

$f = fopen(__DIR__ . '/lock', 'w') or die('Cannot create lock file');
if (flock($f, LOCK_EX | LOCK_NB)) {
    main();
} else {
    $Logger->write([
        'flag' => 'INFO',
        'message' => 'script já em execução'
    ]);
    die('Cannot create lock file');
    exit;
}

function main()
{
    global $Logger;

    $Logger->write([
        'flag' => 'INFO',
        'message' => 'script iniciado'
    ]);

    try {
        $dbh_ixc = getConnectionIxc();
        $dbh_iw = getConnectionIcewarp();

        $stmt = $dbh_ixc->prepare("SELECT cc.id AS id_contrato
        FROM cliente_contrato cc
        WHERE cc.status IN ('D', 'I')
            AND cc.data_cancelamento IS NOT NULL
            AND cc.data_cancelamento != ''
            AND cc.data_cancelamento != '0000-00-00'
            AND cc.data_cancelamento > '2000-01-01'
            AND cc.data_cancelamento <= DATE_ADD(CURDATE(), INTERVAL -120 DAY);");
        $stmt->execute();
        $ids_contratos = $stmt->fetchAll(PDO::FETCH_COLUMN);
        $ids_contratos = implode(", ", $ids_contratos);

        $stmt = $dbh_iw->prepare("SELECT mailadd, id_contrato
        FROM mailbox
        WHERE active=1
            AND id_contrato IN ($ids_contratos);");
        $stmt->execute();
        $emails_a_inativar = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (sizeof($emails_a_inativar) == 0) {
            $Logger->write([
                'flag' => 'INFO',
                'message' => 'Não há e-mails a serem inativados'
            ]);
            goto afterTryCatch;
        }

        foreach ($emails_a_inativar as $email_cancelado) {
            $Logger->write([
                'flag' => 'OPERATION',
                'message' => "E-mail $email_cancelado[mailadd] inativado devido ao contrato cancelado há mais de 120 dias",
                'payload' => $email_cancelado
            ]);
        }

        $stmt = $dbh_iw->prepare("UPDATE mailbox SET active=0 WHERE id_contrato IN ($ids_contratos);");
        $stmt->execute();
    } catch (Exception $e) {
        $Logger->write([
            'flag' => 'ERROR',
            'message' => $e->getMessage()
        ]);
    }

    afterTryCatch:
    $Logger->write([
        'flag' => 'INFO',
        'message' => 'script finalizado'
    ]);
}
