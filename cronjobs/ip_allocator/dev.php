<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/Logger.php';

$CIDADES_IDS_str = file_get_contents(__DIR__ . '/cidades_ids.json');
$CIDADES_IDS = json_decode($CIDADES_IDS_str, true);

$Logger = new Logger('ip_allocator');

$f = fopen(__DIR__ . '/lock.dev', 'w') or die('Cannot create lock file');
if (flock($f, LOCK_EX | LOCK_NB)) {

	$Logger->write([
		'flag' => 'INFO',
		'message' => 'script iniciado'
	]);

	clean_incorrect_ips();
	
	remove_cgnat_from_ip_fix();

	main();

	$Logger->write([
		'flag' => 'INFO',
		'message' => 'script finalizado'
	]);
}
else {
	$Logger->write([
		'flag' => 'INFO',
		'message' => 'script já em execução'
	]);
	die('Cannot create lock file');
	exit;
}

$free_cgnat_ips = [];
$free_public_ips = [];

function main()
{
	// clean_ipv6_conflicts();

	global $Logger, $free_cgnat_ips, $free_public_ips, $CIDADES_IDS;

	$radusuarios = get_radusuarios(50);

	if(sizeof($radusuarios) === 0) {
		$Logger->write([
			'flag' => 'INFO',
			'message' => "finalizando script: nenhum usuário sem IP alocado foi encontrado"
		]);
		return;
	}

	$free_ips = get_all_free_ips();
	$free_cgnat_ips = $free_ips['cgnat'];
	$free_public_ips = $free_ips['public'];

	foreach ($radusuarios as $radusuario) {
		$old_radusuario = $radusuario;

		$login = $radusuario['login'];

		echo "login: $login" . PHP_EOL;


		$transmissor = $radusuario['transmissor'];
		$cidade = $radusuario['cc_cidade'];
		
		// Os logins das ONUs colocadas em Shelters precisam ter IP público
		if (strpos($login, 'shelter.') !== false)
			$radusuario['ip_type'] = 'public';

		// Campestre não possui CGNAT
		// if ($cidade === 'Campestre')
			// $radusuario['ip_type'] = 'public';

		// Jardim Rádio autentica em pool diferente de Jardim Fibra. Como ainda não temos como diferenciar um cliente fibra de um rádio, TODOS OS CLIENTES de Jardim ficarão com os IPs do pool de Andradas
		if ($cidade === 'Santo Antônio do Jardim')
			$cidade = 'Andradas';

		// Caso a cidade seja Caldas ou Águas da Prata, considera como Poços de Caldas para pegar um IP do pool Manhattan
		else if ($cidade === 'Caldas' || $cidade === 'Águas da Prata')
			$cidade = 'Poços de Caldas';

		// Caso a cidade seja São João da Boa Vista e o cliente precise de IP público, utiliza o pool de Pinhal
		else if ($cidade === 'São João da Boa Vista' && $radusuario['ip_type'] == 'public') {
			$cidade = 'Espírito Santo do Pinhal';
		}

		$escopo = null;
		if(substr($transmissor, 0, 2) == 'AP')
			$escopo = $transmissor;
		else
			$escopo = $CIDADES_IDS[$cidade];

		// Caso não possua IPv4, gera um e preenche no IXC
		if(!$radusuario['ip']) {

			$pool_name = get_pool_name($transmissor, $cidade);

			$ip = get_free_ip($pool_name, $radusuario['ip_type']);

			if ($ip === 'not_available') {
				$Logger->write([
					'flag' => 'CRITICAL',
					'message' => "IP do tipo '$radusuario[ip_type]' não disponível no pool '$pool_name'. Nenhum IP foi alocado ao usuário.",
					'login' => $login
				]);
				continue;
			}

			$radusuario['ip'] = $ip;
			echo "IPv4 alocado: $ip".PHP_EOL;
		}

		// Ativa a opção Fixar IP
		$radusuario['fixar_ip'] = 'S';

		// Caso não possua IPv6, gera um e preenche no IXC
		$ipv6_data = [];
		if($escopo && (!$radusuario['pd_ipv6'] || !$radusuario['framed_pd_ipv6'])) {
			if(generate_ipv6($login, $escopo)) {
				$ipv6_data = get_ipv6_data($login);

				$radusuario['pd_ipv6'] = $ipv6_data['delegated_ipv6'];
				$radusuario['framed_pd_ipv6'] = $ipv6_data['framed_ipv6'];

				echo "PD IPv6 alocado: $ipv6_data[delegated_ipv6]".PHP_EOL;
				echo "Framed IPv6 alocado: $ipv6_data[framed_ipv6]".PHP_EOL;
			}
		}
		else if(!$escopo) {
			$Logger->write([
				'flag' => 'ERROR',
				'message' => 'não será possível definir um IPv6 ao usuário: escopo indefinido',
				'login' => $login
			]);
		}

		if($radusuario['pd_ipv6'] && $radusuario['framed_pd_ipv6']) {
			$radusuario['fixar_ipv6'] = 'S';
			$radusuario['framed_fixar_ipv6'] = 'S';
		}

		$new_radusuario = $radusuario;

		// Atualiza apenas se houve mudanças
		if (json_encode($old_radusuario) !== json_encode($new_radusuario)) {
			$response = updateRadusuario($new_radusuario);
			desconectarLogin($radusuario['id']);
		}

		if($response['type'] === 'success') {
			$Logger->write([
				'flag' => 'INFO',
				'message' => 'IP alocado ao usuário',
				'login' => $login,
				'payload' => [
					'login' => $login,
					'ipv4' => isset($ip) ? $ip : '',
					'pd_ipv6' => isset($ipv6_data) ? $ipv6_data['delegated_ipv6'] : '',
					'framed_ipv6' => $ipv6_data ? $ipv6_data['framed_ipv6'] : '',
					'ip_type' => $radusuario['ip_type'],
					'pool_name' => isset($pool_name) ? $pool_name : '',
					'old_radusuario' => $old_radusuario,
					'new_radusuario' => $new_radusuario
				]
			]);
		}
		else {
			$Logger->write([
				'flag' => 'ERROR',
				'message' => utf8_encode($response['message']),
				'login' => $login,
				'payload' => [
					'login' => $login,
					'ipv4' => isset($ip) ? $ip : '',
					'pd_ipv6' => isset($ipv6_data) ? $ipv6_data['delegated_ipv6'] : '',
					'framed_ipv6' => $ipv6_data ? $ipv6_data['framed_ipv6'] : '',
					'ip_type' => $radusuario['ip_type'],
					'pool_name' => isset($pool_name) ? $pool_name : '',
					'old_radusuario' => $old_radusuario,
					'new_radusuario' => $new_radusuario
				]
			]);
		}

		echo PHP_EOL;
	}
}

// Função utilizada apenas para IPv4
function get_pool_name($transmissor, $cidade)
{
	$dbh = getConnectionNoc();

	$pool_name = null;

	if ($transmissor && trim($transmissor) != '' && substr($transmissor, 0, 2) == 'AP') {
		$where_pool_query = "UPPER(service) = UPPER(?)";
		$params_pool_query = array($transmissor);
	} else if ($cidade !== 0 && $cidade !== '0') {
		switch ($cidade) {
			case 'Caldas':
			case 'Águas da Prata':
			case 'Poços de Caldas':
				$city_code = 'Pcs';
				break;
			case 'Andradas':
				$city_code = 'And';
				break;
			case 'Vargem Grande do Sul':
			case 'Espírito Santo do Pinhal':
				$city_code = 'Esp';
				break;
			case 'Santo Antônio do Jardim':
				$city_code = 'Saj';
				break;
			case 'Campestre':
				$city_code = 'Cps';
				break;
			case 'São João da Boa Vista':
				$city_code = 'Sjb';
				break;
		}

		$where_pool_query = "UPPER(pppoeserver) LIKE UPPER(?)";
		$params_pool_query = array('%' . $city_code . '%');
	}

	$stmt = $dbh->prepare("SELECT pppoeserver FROM fdwtables.cad_services WHERE $where_pool_query;");
	$query = $stmt->execute($params_pool_query);

	if ($query && $stmt->rowCount() > 0) {
		$row = $stmt->fetch(PDO::FETCH_ASSOC);
		$pool_name = $row['pppoeserver'];
	}

	return $pool_name;
}

function get_all_free_ips() {
	global $Logger;

	$dbh = getConnectionNoc();

	$query = "SELECT rp.framedipaddress AS ip,
		rp.pool_name
		FROM fdwtables.radippool rp
		INNER JOIN fdwtables.cad_prefix cp ON cp.prefix = rp.prefix
		WHERE host(rp.framedipaddress) NOT IN
			(SELECT ip FROM fdwtables.ixc_radusuarios WHERE ip != '' AND ip IS NOT NULL AND ip ~ '[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}')
			AND host(rp.framedipaddress) NOT IN
			(SELECT framedipaddress AS ip FROM fdwtables.ixc_radacct WHERE acctstoptime IS NULL)
		AND cp.cgnat
		AND NOT rp.framedipaddress <<= ANY (SELECT range FROM network.not_allocated_ranges)
		ORDER BY rp.framedipaddress DESC;";

	$stmt = $dbh->prepare($query);
	$query = $stmt->execute();
	$cgnat_result = $stmt->fetchAll(PDO::FETCH_ASSOC);

	$query = "SELECT rp.framedipaddress AS ip,
		rp.pool_name
		FROM fdwtables.radippool rp
		INNER JOIN fdwtables.cad_prefix cp ON cp.prefix = rp.prefix
		WHERE host(rp.framedipaddress) NOT IN
			(SELECT ip FROM fdwtables.ixc_radusuarios WHERE ip != '' AND ip IS NOT NULL AND ip ~ '[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}')
			AND host(rp.framedipaddress) NOT IN
			(SELECT framedipaddress AS ip FROM fdwtables.ixc_radacct WHERE acctstoptime IS NULL)
		AND NOT cp.cgnat
		AND rp.framedipaddress NOT IN ('*************')
		AND NOT rp.framedipaddress <<= ANY (SELECT range FROM network.not_allocated_ranges)
		ORDER BY rp.framedipaddress DESC";

	$stmt = $dbh->prepare($query);
	$query = $stmt->execute();
	$public_result = $stmt->fetchAll(PDO::FETCH_ASSOC);

	$cgnat_ips = [];
	$public_ips = [];

	foreach ($cgnat_result as $cgnat_row) {
		if (!isset($cgnat_ips[$cgnat_row['pool_name']]))
			$cgnat_ips[$cgnat_row['pool_name']] = [];

		$cgnat_ips[$cgnat_row['pool_name']][] = $cgnat_row['ip'];
	}

	foreach ($public_result as $public_row) {
		if (!isset($public_ips[$public_row['pool_name']]))
			$public_ips[$public_row['pool_name']] = [];

		$public_ips[$public_row['pool_name']][] = $public_row['ip'];
	}

	foreach ($public_ips as $pool_name => $pool)
		if(sizeof($pool) < IPS_QTY_WARNING)
			$Logger->write([
				'flag' => 'WARNING',
				'message' => "o pool '$pool_name' possui apenas " . sizeof($pool) . " IPs do tipo 'public' disponíveis"
			]);

	foreach ($cgnat_ips as $pool_name => $pool)
		if (sizeof($pool) < IPS_QTY_WARNING)
			$Logger->write([
				'flag' => 'WARNING',
				'message' => "o pool '$pool_name' possui apenas " . sizeof($pool) . " IPs do tipo 'cgnat' disponíveis"
			]);

	return array(
		'cgnat' => $cgnat_ips,
		'public' => $public_ips
	);
}

function get_radusuarios($limit = false) {
	$dbh_ixc = getConnectionIxc();

	$limitSQL = ($limit) ? "LIMIT $limit" : "";

	$stmt = $dbh_ixc->prepare("SELECT r.*,
	rpr.descricao AS transmissor,
	(CASE
		WHEN r.endereco_padrao_cliente = 'S' OR (r.endereco_padrao_cliente = 'N' AND (r.cidade = 0 OR r.cidade IS NULL)) THEN
		ci2.nome
		ELSE ci1.nome
	END) AS cc_cidade,
	(CASE
		WHEN GROUP_CONCAT(vcp.descricao) LIKE '%IP%FIX%' THEN 'public'
		ELSE 'nat'
	END) AS ip_type,
	GROUP_CONCAT(vcp.descricao) AS produtos
	FROM radusuarios r
	INNER JOIN cliente cl ON cl.id = r.id_cliente
	LEFT JOIN radpop_radio rpr ON rpr.id = r.id_transmissor
	LEFT JOIN cidade ci1 ON ci1.id = r.cidade	 
	LEFT JOIN cidade ci2 ON ci2.id = cl.cidade
	INNER JOIN cliente_contrato cc ON cc.id = r.id_contrato
	INNER JOIN vd_contratos_produtos vcp
		ON vcp.descricao != '' AND vcp.descricao IS NOT NULL
		AND vcp.descricao NOT LIKE 'MailOnly%'
		AND vcp.descricao NOT LIKE 'SERVIÇO DE E-MAIL%'
		AND vcp.descricao NOT LIKE 'Hospedagem%'
		AND vcp.descricao NOT LIKE 'VELOX%'
		AND vcp.descricao NOT LIKE 'ENSINO ONLINE%'
		AND vcp.descricao NOT LIKE 'Publicidade-Banner sup.rotativ%'
		AND vcp.descricao NOT LIKE 'Plano ilimitado%'
		AND vcp.descricao NOT LIKE '%20Hrs%'
		AND vcp.descricao NOT LIKE '%30Hrs%'
		AND vcp.descricao NOT LIKE '%Usuario Gratuito%'
		AND vcp.descricao NOT LIKE '%Plano Gratuito%'
		AND vcp.descricao NOT LIKE 'SET TOP BOX'
		AND vcp.descricao NOT LIKE 'Plano CMD%'
		AND vcp.descricao NOT LIKE 'PCS-COMERCIAL'
		AND vcp.descricao NOT LIKE 'PCS-SUPORTE'
		AND vcp.descricao NOT LIKE '%provisorio%'
		AND vcp.descricao NOT LIKE '%DEDICADO%'
		AND vcp.descricao NOT LIKE 'DVI Ilimit. Gratuito/Parceria'
		AND vcp.descricao NOT LIKE 'Rádio JDM 256K sem Comodato PJ'
		AND (vcp.id_vd_contrato = cc.id_vd_contrato OR vcp.id_contrato = cc.id)
	WHERE
		r.ativo = 'S'
		AND (r.ip = '' OR r.ip IS NULL
			
			OR r.pd_ipv6 = '' OR r.pd_ipv6 IS NULL
			OR r.framed_pd_ipv6 = '' OR r.framed_pd_ipv6 IS NULL
			OR r.fixar_ip != 'S' OR r.fixar_ipv6 != 'S' OR r.framed_fixar_ipv6 != 'S'
			
		)
		AND cc.status IN ('A', 'P')
		AND cc.status_internet IN ('A', 'AA')
	GROUP BY r.login
	ORDER BY ip_type DESC
	$limitSQL;");

	$stmt->execute();
	$result = $stmt->fetchAll(PDO::FETCH_ASSOC);

	return $result;
}

function get_free_ip($pool_name, $type) {
	global $free_cgnat_ips, $free_public_ips;

	if ($type === 'public') {
		if (isset($free_public_ips[$pool_name][0]))
			return array_shift($free_public_ips[$pool_name]);
		else
			return 'not_available';
	} else if ($type === 'cgnat' || $type === 'nat') {
		if (isset($free_cgnat_ips[$pool_name][0]))
			return array_shift($free_cgnat_ips[$pool_name]);
		else
			return 'not_available';
	}
}

function generate_ipv6($login, $escopo) {
	$dbh_radius = getConnectionRadius();
	$stmt = $dbh_radius->prepare("SELECT insert_radipv6(?, ?, ?)");
	return $stmt->execute([$login, $escopo, 1]);
}

function get_ipv6_data($login) {
	global $Logger;

	$retorno = [];

	$dbh_radius = getConnectionRadius();
	$stmt = $dbh_radius->prepare("SELECT * FROM public.radipv6_allocated WHERE username = ?;");
	$stmt->execute([$login]);
	$radipv6_rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

	$framed_ipv6 = $delegated_ipv6 = null;
	foreach ($radipv6_rows as $row) {
		if ($row['attribute'] == 'Framed-IPv6-Prefix')
			$framed_ipv6 = $row['allocated_prefix'];
		else if ($row['attribute'] == 'Delegated-Ipv6-Prefix')
			$delegated_ipv6 = $row['allocated_prefix'];
	}

	if ($framed_ipv6 && $delegated_ipv6) {
		$retorno['delegated_ipv6'] = $delegated_ipv6;
		$retorno['framed_ipv6'] = $framed_ipv6;
	} else {
		$Logger->write([
			'flag' => 'CRITICAL',
			'message' => "não foi possível definir a faixa de IPv6 do usuário",
			'login' => $login
		]);
	}

	return $retorno;
}

function clean_ipv6_conflicts() {
	$dbh_ixc = getConnectionIxc();
	$stmt = $dbh_ixc->prepare("SELECT *
		FROM radusuarios
		WHERE pd_ipv6 IN (
			SELECT pd_ipv6 FROM (
				SELECT 
					pd_ipv6, 
					COUNT(pd_ipv6)
				FROM
					radusuarios
				WHERE ativo = 'S'
				GROUP BY pd_ipv6
				HAVING COUNT(pd_ipv6) > 1
				) sub
			)
			AND pd_ipv6 IS NOT NULL AND pd_ipv6 != ''
			ORDER BY pd_ipv6;");
	$stmt->execute();
	$conflictingRadusuarios = $stmt->fetchAll(PDO::FETCH_ASSOC);

	if (sizeof($conflictingRadusuarios) == 0)
		return;
	
	$i = 0;
	foreach($conflictingRadusuarios as $radusuario) {
		$radusuario['pd_ipv6'] = '';
		$radusuario['framed_pd_ipv6'] = '';
		$radusuario['fixar_ipv6'] = 'H';
		$radusuario['framed_fixar_ipv6'] = 'H';

		updateRadusuario($radusuario, true);

		echo "updated $radusuario[login]".PHP_EOL;
	}
}

function clean_incorrect_ips() {
	global $Logger;

	$dbh_noc = getConnectionNoc();
	$stmt = $dbh_noc->prepare("SELECT
		sub.*,
		cpo.ip_type,
		cpo.pool AS pool_correto,
		cpx.pool_name AS pool_alocado
	FROM (
		SELECT
			ru.login,
			ru.ip,
			(CASE
				WHEN ru.endereco_padrao_cliente = 'S' THEN ci3.nome
				WHEN ru.endereco_padrao_cliente = 'N' AND ru.cidade != 0 AND ru.cidade IS NOT NULL THEN ci1.nome
				WHEN cc.endereco_padrao_cliente = 'N' AND cc.cidade != 0 AND cc.cidade IS NOT NULL THEN ci2.nome
				ELSE ci3.nome
			END) AS cidade
		FROM fdwtables.ixc_radusuarios ru
		LEFT JOIN fdwtables.ixc_cliente_contrato cc ON cc.id = ru.id_contrato
		LEFT JOIN fdwtables.ixc_cliente cl ON cl.id = cc.id_cliente
		LEFT JOIN fdwtables.ixc_cidade ci1 ON ci1.id = ru.cidade
		LEFT JOIN fdwtables.ixc_cidade ci2 ON ci2.id = cc.cidade
		LEFT JOIN fdwtables.ixc_cidade ci3 ON ci3.id = cl.cidade
		WHERE cc.status NOT IN ('D', 'I')
			AND cc.id_cliente NOT IN (
				20971 -- Ignora os logins internos da Telemidia
			)
			AND ru.ativo = 'S'
			AND ru.ip LIKE '%.%.%.%'
			AND ru.ip::inet <<= '**********/10'
			AND ru.login NOT IN (
				'carlos.xavier',
				'decioa.zamboni',
				'cristianepalini'
			)
		) sub
	LEFT JOIN fdwtables.cidades_pools cpo ON cpo.cidade = sub.cidade
		AND (
			(sub.ip::inet <<= '**********/10' AND cpo.ip_type = 'cgnat')
			OR
			(NOT sub.ip::inet <<= '**********/10' AND cpo.ip_type = 'public')
		)
	INNER JOIN fdwtables.cad_prefix cpx ON sub.ip::inet <<= cpx.prefix
		AND cpx.pool_name != cpo.pool;
	");

	$stmt->execute();

	$logins_incorretos = $stmt->fetchAll(PDO::FETCH_ASSOC);

	$dbh_ixc = getConnectionIxc();
	foreach($logins_incorretos as $login) {
		$Logger->write([
			'flag' => 'INFO',
			'message' => "Login com IP incorreto: '$login[login]'. Será removido o IP para ser realocado em seguida.",
			'payload' => $login
		]);

		$stmt = $dbh_ixc->prepare("SELECT * FROM radusuarios WHERE login = ?;");
		$stmt->execute(array($login['login']));
		$radusuario = $stmt->fetch(PDO::FETCH_ASSOC);

		$radusuario['ip'] = '';
		$radusuario['fixar_ip'] = 'N';
		$response = updateRadusuario($radusuario);
		desconectarLogin($radusuario['id']);
	}
}

function remove_cgnat_from_ip_fix() {
	global $Logger;

	$dbh_ixc = getConnectionIxc();
	$stmt = $dbh_ixc->prepare("SELECT DISTINCT
		ru.*,
		vcp.descricao AS pacote_fix
		FROM radusuarios ru
		LEFT JOIN cliente_contrato cc ON cc.id = ru.id_contrato
		INNER JOIN vd_contratos_produtos vcp ON (vcp.id_contrato = cc.id OR vcp.id_vd_contrato = cc.id_vd_contrato) 
			AND (
				vcp.descricao LIKE '%IP FIXO%'
				OR vcp.descricao LIKE '%IP-FIXO%'
			)
		WHERE (INET_ATON(ru.ip) BETWEEN INET_ATON('**********') AND INET_ATON('***************'));
		");
	$stmt->execute();
	$logins_cgnat = $stmt->fetchAll(PDO::FETCH_ASSOC);

	foreach($logins_cgnat as $radusuario) {
		$Logger->write
		([
			'flag' => 'INFO',
			'message' => "Login com IP de CGNAT, porém possui pacote IP Fix / IP Fixo: '$radusuario[login]'. Será removido o IP para ser realocado um IP público em seguida.",
			'login' => $radusuario['login'],
			'payload' => [
				'login' => $radusuario['login'],
				'ip_cgnat' => $radusuario['ip'],
				'pacote_fix' => $radusuario['pacote_fix']
			]
		]);
		
		$radusuario['ip'] = '';
		$radusuario['fixar_ip'] = 'N';

		$response = updateRadusuario($radusuario);
		desconectarLogin($radusuario['id']);
	}
}