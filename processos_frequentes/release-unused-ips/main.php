<?php
require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/Logger.php';

$f = fopen('lock', 'w') or die('Cannot create lock file');
if (flock($f, LOCK_EX | LOCK_NB)) {
	main();
}

$Logger;

function main()
{
	global $Logger, $free_cgnat_ips, $free_public_ips;

	$Logger = new Logger('ip_allocator');

	$Logger->write([
		'flag' => 'VERBOSE',
		'message' => 'script iniciado'
	]);

	$dbh_noc = getConnectionIxc();
	$stmt = $dbh_noc->prepare("SELECT *
	FROM radusuarios
	WHERE ativo = 'N' AND (
		(ip IS NOT NULL AND ip != '')
		OR
		(pd_ipv6 IS NOT NULL AND pd_ipv6 != '')
		OR
		(framed_pd_ipv6 IS NOT NULL AND framed_pd_ipv6 != '')
	);");
	$stmt->execute();
	$rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

	$cleaned_logins = [];
	$i = 0;
	foreach ($rows as $radusuario) {
		if ($i++ == 2)
			break;

		echo "Login: $radusuario[login]".PHP_EOL;

		$radusuario['ip'] = '';
		$radusuario['pd_ipv6'] = '';
		$radusuario['framed_pd_ipv6'] = '';

		$radusuario['fixar_ip'] = 'H';
		$radusuario['fixar_ipv6'] = 'H';
		$radusuario['framed_fixar_ipv6'] = 'H';

		$resposta = updateRadusuario($radusuario);

		if($resposta['type'] === 'success'
			&& $resposta['message'] === 'Registro atualizado com sucesso!') {
			$cleaned_logins[] = $radusuario['login'];
		}
	}

	var_dump($cleaned_logins);
}