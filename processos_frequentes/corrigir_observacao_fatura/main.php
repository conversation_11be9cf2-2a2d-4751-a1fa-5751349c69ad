<?php
// Procurar pelas faturas que estão com a observação incorreta:
$searchFaturas = [
    'observacao' => 'de 01/03/2025 até 31/03/2025',
    'data_vencimento' => '2025-03-30',
    'data_emissao' => '2025-02-27'
];

$novaDataInicial = '01/02/2025';
$novaDataFinal = '28/02/2025';

$novaObservacao = "de $novaDataInicial até $novaDataFinal";

require_once __DIR__ . '/../../common/config.php';
require_once __DIR__ . '/../../common/utils.php';
require_once __DIR__ . '/../../common/Logger.php';

$api = getIxcApi();

$params = array(
    'qtype' => 'fn_areceber.id',
    'query' => '',
    'oper' => '=',
    'sortname' => 'fn_areceber.id',
    'sortorder' => 'asc',
    'page' => '1',
    'rp' => '99000',
    'grid_param' => json_encode([
        [
            'TB' => 'fn_areceber.obs',
            'OP' => '=',
            'P' => $searchFaturas['observacao']
        ],
        [
            'TB' => 'fn_areceber.data_vencimento',
            'OP' => '=',
            'P' => $searchFaturas['data_vencimento']
        ],
        [
            'TB' => 'fn_areceber.data_emissao',
            'OP' => '=',
            'P' => $searchFaturas['data_emissao']
        ]
    ])
);

$api->get('fn_areceber', $params);
$retorno = $api->getRespostaConteudo(true);

$idsErros = [];
$counter=1;
foreach ($retorno['registros'] as $registro) {
    // Corrige a codificação dos campos string
    foreach ($registro as &$value)
		if (gettype($value) === 'string')
			$value = utf8_encode($value);

    // var_dump apenas para debug, comentar em produção:
    // var_dump($registro);
    // echo $counter++;
    // continue;

    $registro['obs'] = $novaObservacao;
    $registro['data_vencimento'] = reverseDate($registro['data_vencimento']);
    $registro['data_emissao'] = reverseDate($registro['data_emissao']);

    $registro['data_inicial'] = $novaDataInicial;
    $registro['data_final'] = $novaDataFinal;
    
    // Executa a alteração das faturas, descomentar em produção:
    $api->put('fn_areceber_altera', $registro, $registro['id']);
    $retorno = $api->getRespostaConteudo(true);

    if ($retorno['type'] === 'success') {
        echo 'Registro atualizado com sucesso: ID ' . $registro['id'] . PHP_EOL;
    }
    else {
        echo 'Erro ao atualizar registro: ID ' . $registro['id'] . PHP_EOL;
        $idsErros[$registro['id']] = $retorno['message'];
    }

    // Break para debug, para executar apenas 1 vez (descomentar em produção)
    // break;
}

echo 'Execução finalizada.' . PHP_EOL;
echo 'Registros com erros ao atualizar ([ID] => Erro):' . PHP_EOL;
var_dump($idsErros);

function reverseDate($date) {
    return implode('-', array_reverse(explode('-', $date)));
}



/*
[PUT] https://HOST/webservice/v1/fn_areceber_altera/ID_DO_BOLETO


mas você deve se atentar aos campos:

"data_vencimento": "2023-11-10"
"data_emissao": "2022-11-18"

eles vão vir no formato americano, e você vai precisar passar eles na requisição de alteração no formato brasileiro, então eles ficariam da seguinte forma:

"data_vencimento": "10-11-2023"
"data_emissao": "18-11-2022"
*/